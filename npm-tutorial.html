<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NPM 完整教程</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #cc3534;
            text-align: center;
            border-bottom: 3px solid #cc3534;
            padding-bottom: 10px;
        }
        h2 {
            color: #2c3e50;
            margin-top: 30px;
            padding: 10px;
            background-color: #ecf0f1;
            border-left: 5px solid #cc3534;
        }
        h3 {
            color: #34495e;
            margin-top: 25px;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e74c3c;
        }
        pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
        }
        pre code {
            background: none;
            color: inherit;
            padding: 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #cc3534;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .toc {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .toc ul {
            list-style-type: none;
            padding-left: 0;
        }
        .toc li {
            margin: 8px 0;
        }
        .toc a {
            text-decoration: none;
            color: #cc3534;
            font-weight: 500;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📦 NPM 完整教程指南</h1>
        
        <div class="info">
            <strong>关于本教程：</strong> 本教程将从零开始教你掌握NPM（Node Package Manager），包括基础概念、常用命令、高级特性和最佳实践。
        </div>

        <div class="toc">
            <h3>📚 目录</h3>
            <ul>
                <li><a href="#what-is-npm">1. 什么是NPM？</a></li>
                <li><a href="#installation">2. 安装和设置</a></li>
                <li><a href="#basic-concepts">3. 基本概念</a></li>
                <li><a href="#package-json">4. package.json 详解</a></li>
                <li><a href="#basic-commands">5. 基础命令</a></li>
                <li><a href="#dependencies">6. 依赖管理</a></li>
                <li><a href="#scripts">7. NPM Scripts</a></li>
                <li><a href="#publishing">8. 发布包</a></li>
                <li><a href="#advanced">9. 高级特性</a></li>
                <li><a href="#best-practices">10. 最佳实践</a></li>
            </ul>
        </div>

        <h2 id="what-is-npm">1. 什么是NPM？</h2>
        
        <p>NPM（Node Package Manager）是 Node.js 的包管理器，也是世界上最大的软件注册表。它包含三个组件：</p>
        
        <ul>
            <li><strong>网站：</strong> 用于发现包、设置配置文件和管理npm体验的其他方面</li>
            <li><strong>命令行界面 (CLI)：</strong> 在终端中运行，开发者通过它与npm进行交互</li>
            <li><strong>注册表：</strong> 一个大型公共JavaScript软件数据库及其周围的元信息</li>
        </ul>

        <h3>NPM的主要功能：</h3>
        <ul>
            <li>安装和管理项目依赖</li>
            <li>运行脚本和任务</li>
            <li>发布和分享代码包</li>
            <li>版本控制和语义化版本</li>
        </ul>

        <h2 id="installation">2. 安装和设置</h2>
        
        <h3>2.1 安装 Node.js 和 NPM</h3>
        <p>NPM 随 Node.js 一起安装。访问 <a href="https://nodejs.org/">nodejs.org</a> 下载并安装最新版本。</p>
        
        <h3>2.2 验证安装</h3>
        <pre><code># 检查 Node.js 版本
node --version

# 检查 NPM 版本
npm --version</code></pre>

        <h3>2.3 配置 NPM</h3>
        <pre><code># 设置用户信息
npm config set init-author-name "你的名字"
npm config set init-author-email "<EMAIL>"
npm config set init-license "MIT"

# 查看当前配置
npm config list</code></pre>

        <h2 id="basic-concepts">3. 基本概念</h2>
        
        <h3>3.1 包（Package）</h3>
        <p>包是一个包含 <code>package.json</code> 文件的文件夹或文件。</p>
        
        <h3>3.2 模块（Module）</h3>
        <p>模块是能被 Node.js 的 <code>require()</code> 函数加载的任何东西。</p>
        
        <h3>3.3 依赖（Dependencies）</h3>
        <ul>
            <li><strong>dependencies：</strong> 生产环境需要的包</li>
            <li><strong>devDependencies：</strong> 只在开发环境需要的包</li>
            <li><strong>peerDependencies：</strong> 对等依赖</li>
            <li><strong>optionalDependencies：</strong> 可选依赖</li>
        </ul>

        <h2 id="package-json">4. package.json 详解</h2>
        
        <p><code>package.json</code> 是项目的清单文件，包含项目的元数据和依赖信息。</p>
        
        <h3>4.1 创建 package.json</h3>
        <pre><code># 交互式创建
npm init

# 使用默认值快速创建
npm init -y</code></pre>

        <h3>4.2 package.json 结构示例</h3>
        <pre><code>{
  "name": "my-awesome-project",
  "version": "1.0.0",
  "description": "一个很棒的项目",
  "main": "index.js",
  "scripts": {
    "start": "node index.js",
    "test": "jest",
    "build": "webpack --mode production",
    "dev": "nodemon index.js"
  },
  "keywords": ["javascript", "nodejs", "npm"],
  "author": "你的名字 &lt;<EMAIL>&gt;",
  "license": "MIT",
  "dependencies": {
    "express": "^4.18.2",
    "lodash": "^4.17.21"
  },
  "devDependencies": {
    "jest": "^29.0.0",
    "nodemon": "^2.0.20"
  },
  "engines": {
    "node": "&gt;=14.0.0",
    "npm": "&gt;=6.0.0"
  }
}</code></pre>

        <h3>4.3 重要字段说明</h3>
        <table>
            <tr>
                <th>字段</th>
                <th>描述</th>
                <th>必需</th>
            </tr>
            <tr>
                <td>name</td>
                <td>包的名称</td>
                <td>是</td>
            </tr>
            <tr>
                <td>version</td>
                <td>包的版本</td>
                <td>是</td>
            </tr>
            <tr>
                <td>description</td>
                <td>包的描述</td>
                <td>否</td>
            </tr>
            <tr>
                <td>main</td>
                <td>入口文件</td>
                <td>否</td>
            </tr>
            <tr>
                <td>scripts</td>
                <td>可运行的脚本</td>
                <td>否</td>
            </tr>
            <tr>
                <td>dependencies</td>
                <td>生产依赖</td>
                <td>否</td>
            </tr>
            <tr>
                <td>devDependencies</td>
                <td>开发依赖</td>
                <td>否</td>
            </tr>
        </table>

        <h2 id="basic-commands">5. 基础命令</h2>
        
        <h3>5.1 安装包</h3>
        <pre><code># 安装单个包
npm install express

# 安装多个包
npm install express lodash moment

# 安装并保存到 dependencies
npm install --save express

# 安装并保存到 devDependencies
npm install --save-dev jest

# 全局安装
npm install -g nodemon

# 安装特定版本
npm install express@4.18.2</code></pre>

        <h3>5.2 卸载包</h3>
        <pre><code># 卸载包
npm uninstall express

# 卸载并从 package.json 中移除
npm uninstall --save express

# 全局卸载
npm uninstall -g nodemon</code></pre>

        <h3>5.3 更新包</h3>
        <pre><code># 更新所有包
npm update

# 更新特定包
npm update express

# 检查过时的包
npm outdated</code></pre>

        <h3>5.4 查看包信息</h3>
        <pre><code># 查看已安装的包
npm list

# 查看全局安装的包
npm list -g

# 查看包的详细信息
npm info express

# 查看包的版本历史
npm view express versions --json</code></pre>

        <h2 id="dependencies">6. 依赖管理</h2>
        
        <h3>6.1 版本控制</h3>
        <p>NPM 使用语义化版本（SemVer）：<code>主版本.次版本.修订版本</code></p>
        
        <table>
            <tr>
                <th>符号</th>
                <th>含义</th>
                <th>示例</th>
                <th>匹配版本</th>
            </tr>
            <tr>
                <td>^</td>
                <td>兼容版本</td>
                <td>^1.2.3</td>
                <td>≥1.2.3 &lt;2.0.0</td>
            </tr>
            <tr>
                <td>~</td>
                <td>近似版本</td>
                <td>~1.2.3</td>
                <td>≥1.2.3 &lt;1.3.0</td>
            </tr>
            <tr>
                <td>*</td>
                <td>任意版本</td>
                <td>*</td>
                <td>任何版本</td>
            </tr>
            <tr>
                <td>无符号</td>
                <td>精确版本</td>
                <td>1.2.3</td>
                <td>1.2.3</td>
            </tr>
        </table>

        <h3>6.2 package-lock.json</h3>
        <div class="info">
            <strong>重要：</strong> <code>package-lock.json</code> 文件锁定依赖的确切版本，确保团队成员安装相同版本的依赖。应该提交到版本控制系统中。
        </div>

        <h3>6.3 清理和修复</h3>
        <pre><code># 清理 node_modules
rm -rf node_modules package-lock.json

# 重新安装
npm install

# 审计安全漏洞
npm audit

# 自动修复安全问题
npm audit fix</code></pre>

        <h2 id="scripts">7. NPM Scripts</h2>
        
        <p>NPM Scripts 是在 <code>package.json</code> 中定义的可执行脚本。</p>
        
        <h3>7.1 基本用法</h3>
        <pre><code>{
  "scripts": {
    "start": "node server.js",
    "dev": "nodemon server.js",
    "test": "jest",
    "build": "webpack --mode production",
    "lint": "eslint src/",
    "format": "prettier --write src/"
  }
}</code></pre>

        <h3>7.2 运行脚本</h3>
        <pre><code># 运行 start 脚本
npm start

# 运行其他脚本
npm run dev
npm run test
npm run build</code></pre>

        <h3>7.3 脚本钩子</h3>
        <pre><code>{
  "scripts": {
    "prebuild": "npm run lint",
    "build": "webpack",
    "postbuild": "npm run test"
  }
}</code></pre>

        <h3>7.4 传递参数</h3>
        <pre><code># 在脚本中
"test": "jest"

# 运行时传递参数
npm run test -- --watch</code></pre>

        <h2 id="publishing">8. 发布包</h2>
        
        <h3>8.1 准备发布</h3>
        <pre><code># 创建 npm 账户
npm adduser

# 登录
npm login

# 检查登录状态
npm whoami</code></pre>

        <h3>8.2 发布流程</h3>
        <pre><code># 检查包名是否可用
npm search your-package-name

# 发布包
npm publish

# 发布预发布版本
npm publish --tag beta</code></pre>

        <div class="warning">
            <strong>注意：</strong> 
            <ul>
                <li>包名必须唯一</li>
                <li>版本号不能重复</li>
                <li>确保 <code>.npmignore</code> 文件正确配置</li>
            </ul>
        </div>

        <h3>8.3 版本管理</h3>
        <pre><code># 更新版本号
npm version patch   # 1.0.0 -> 1.0.1
npm version minor   # 1.0.0 -> 1.1.0
npm version major   # 1.0.0 -> 2.0.0

# 带标签的版本
npm version prerelease --preid=alpha  # 1.0.0 -> 1.0.1-alpha.0</code></pre>

        <h2 id="advanced">9. 高级特性</h2>
        
        <h3>9.1 NPM 配置</h3>
        <pre><code># 查看所有配置
npm config list -l

# 设置注册源
npm config set registry https://registry.npmjs.org/

# 使用淘宝镜像
npm config set registry https://registry.npmmirror.com/

# 设置代理
npm config set proxy http://proxy.company.com:8080</code></pre>

        <h3>9.2 .npmrc 文件</h3>
        <pre><code># 项目级别 (.npmrc)
registry=https://registry.npmmirror.com/
save-exact=true
init-license=MIT</code></pre>

        <h3>9.3 npm link</h3>
        <p>用于本地开发时链接包：</p>
        <pre><code># 在要链接的包目录中
npm link

# 在使用包的项目中
npm link package-name</code></pre>

        <h3>9.4 工作空间（Workspaces）</h3>
        <p>管理单体仓库中的多个包：</p>
        <pre><code>{
  "workspaces": [
    "packages/*",
    "apps/*"
  ]
}</code></pre>

        <h2 id="best-practices">10. 最佳实践</h2>
        
        <h3>10.1 安全性</h3>
        <ul>
            <li>定期运行 <code>npm audit</code> 检查安全漏洞</li>
            <li>使用 <code>npm ci</code> 在生产环境中安装依赖</li>
            <li>避免使用 <code>sudo</code> 运行 npm 命令</li>
        </ul>

        <h3>10.2 性能优化</h3>
        <pre><code># 使用 npm ci 替代 npm install（在 CI/CD 中）
npm ci

# 清理缓存
npm cache clean --force

# 设置缓存目录
npm config set cache /path/to/cache</code></pre>

        <h3>10.3 项目组织</h3>
        <ul>
            <li>合理使用 <code>dependencies</code> 和 <code>devDependencies</code></li>
            <li>保持 <code>package-lock.json</code> 在版本控制中</li>
            <li>使用语义化版本控制</li>
            <li>编写有意义的脚本命令</li>
        </ul>

        <h3>10.4 常用工具推荐</h3>
        <table>
            <tr>
                <th>类别</th>
                <th>工具</th>
                <th>用途</th>
            </tr>
            <tr>
                <td>代码质量</td>
                <td>ESLint, Prettier</td>
                <td>代码检查和格式化</td>
            </tr>
            <tr>
                <td>测试</td>
                <td>Jest, Mocha</td>
                <td>单元测试</td>
            </tr>
            <tr>
                <td>构建</td>
                <td>Webpack, Rollup</td>
                <td>模块打包</td>
            </tr>
            <tr>
                <td>开发</td>
                <td>Nodemon, Live Server</td>
                <td>开发服务器</td>
            </tr>
        </table>

        <div class="success">
            <h3>🎉 恭喜！</h3>
            <p>你已经完成了NPM的完整学习！现在你应该能够：</p>
            <ul>
                <li>理解NPM的基本概念和工作原理</li>
                <li>熟练使用NPM命令管理项目依赖</li>
                <li>创建和配置package.json文件</li>
                <li>编写和运行NPM脚本</li>
                <li>发布自己的NPM包</li>
                <li>应用NPM的最佳实践</li>
            </ul>
        </div>

        <h3>📚 进一步学习资源</h3>
        <ul>
            <li><a href="https://docs.npmjs.com/">NPM 官方文档</a></li>
            <li><a href="https://nodejs.org/en/docs/">Node.js 官方文档</a></li>
            <li><a href="https://semver.org/">语义化版本规范</a></li>
        </ul>

        <footer style="margin-top: 50px; padding: 20px; background-color: #2c3e50; color: white; text-align: center; border-radius: 8px;">
            <p>© 2025 NPM 教程 | 学习愉快！ 🚀</p>
        </footer>
    </div>
</body>
</html>