<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>vLLM 完整教程</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #6f42c1;
            text-align: center;
            border-bottom: 3px solid #6f42c1;
            padding-bottom: 10px;
        }
        h2 {
            color: #2c3e50;
            margin-top: 30px;
            padding: 10px;
            background-color: #ecf0f1;
            border-left: 5px solid #6f42c1;
        }
        h3 {
            color: #34495e;
            margin-top: 25px;
        }
        code {
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e74c3c;
        }
        pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
        }
        pre code {
            background-color: transparent;
            color: #ecf0f1;
            padding: 0;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .toc {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .toc ul {
            margin: 0;
            padding-left: 20px;
        }
        .toc a {
            color: #6f42c1;
            text-decoration: none;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #6f42c1;
            color: white;
        }
        .back-button {
            display: inline-block;
            background-color: #6f42c1;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin-bottom: 20px;
            transition: background-color 0.3s;
        }
        .back-button:hover {
            background-color: #5a2d91;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.html" class="back-button">← 返回主页</a>
        
        <h1>🚀 vLLM 完整教程</h1>
        
        <div class="toc">
            <h3>📋 目录</h3>
            <ul>
                <li><a href="#introduction">1. vLLM 简介</a></li>
                <li><a href="#installation">2. 安装与环境配置</a></li>
                <li><a href="#quickstart">3. 快速开始</a></li>
                <li><a href="#offline-inference">4. 离线推理</a></li>
                <li><a href="#api-server">5. API 服务器部署</a></li>
                <li><a href="#optimization">6. 性能优化</a></li>
                <li><a href="#distributed">7. 分布式推理</a></li>
                <li><a href="#best-practices">8. 最佳实践</a></li>
                <li><a href="#troubleshooting">9. 常见问题</a></li>
            </ul>
        </div>

        <section id="introduction">
            <h2>1. vLLM 简介</h2>
            <p>vLLM 是一个高性能的大语言模型推理和服务库，具有以下特点：</p>
            <ul>
                <li><strong>高吞吐量</strong>：通过 PagedAttention 算法实现高效的内存管理</li>
                <li><strong>灵活性</strong>：支持多种模型架构和采样算法</li>
                <li><strong>易用性</strong>：提供简单的 Python API 和兼容 OpenAI 的 REST API</li>
                <li><strong>分布式</strong>：支持多 GPU 和多节点推理</li>
            </ul>

            <div class="info">
                <strong>💡 核心技术 - PagedAttention：</strong><br>
                vLLM 的核心创新是 PagedAttention 算法，它将注意力计算中的 KV 缓存按页管理，类似于操作系统的虚拟内存，大大提高了内存利用效率。
            </div>
        </section>

        <section id="installation">
            <h2>2. 安装与环境配置</h2>
            
            <h3>2.1 系统要求</h3>
            <ul>
                <li>Linux 操作系统</li>
                <li>Python 3.8-3.11</li>
                <li>CUDA 11.8 或更高版本</li>
                <li>GPU：支持计算能力 7.0+ 的 NVIDIA GPU</li>
            </ul>

            <h3>2.2 安装方法</h3>
            
            <h4>方法一：从 PyPI 安装（推荐）</h4>
            <pre><code># 安装 vLLM
pip install vllm

# 如果需要支持特定的 CUDA 版本
pip install vllm --extra-index-url https://download.pytorch.org/whl/cu118</code></pre>

            <h4>方法二：从源码安装</h4>
            <pre><code># 克隆仓库
git clone https://github.com/vllm-project/vllm.git
cd vllm

# 安装依赖
pip install -e .</code></pre>

            <h3>2.3 Docker 安装</h3>
            <pre><code># 拉取官方镜像
docker pull vllm/vllm-openai:latest

# 运行容器
docker run --gpus all \
    -v ~/.cache/huggingface:/root/.cache/huggingface \
    -p 8000:8000 \
    --ipc=host \
    vllm/vllm-openai:latest \
    --model microsoft/DialoGPT-medium</code></pre>

            <div class="warning">
                <strong>⚠️ 注意：</strong> vLLM 目前主要支持 Linux 系统，在 Windows 和 macOS 上可能会遇到兼容性问题。
            </div>
        </section>

        <section id="quickstart">
            <h2>3. 快速开始</h2>
            
            <h3>3.1 基本用法</h3>
            <pre><code>from vllm import LLM, SamplingParams

# 创建 LLM 实例
llm = LLM(model="microsoft/DialoGPT-medium")

# 设置采样参数
sampling_params = SamplingParams(temperature=0.8, top_p=0.95)

# 准备提示词
prompts = [
    "Hello, my name is",
    "The president of the United States is",
    "The capital of France is",
]

# 生成回复
outputs = llm.generate(prompts, sampling_params)

# 输出结果
for output in outputs:
    prompt = output.prompt
    generated_text = output.outputs[0].text
    print(f"Prompt: {prompt!r}, Generated text: {generated_text!r}")</code></pre>

            <h3>3.2 批量推理示例</h3>
            <pre><code>import torch
from vllm import LLM, SamplingParams

# 检查 GPU 可用性
if torch.cuda.is_available():
    print(f"GPU 数量: {torch.cuda.device_count()}")
    print(f"当前 GPU: {torch.cuda.get_device_name()}")

# 初始化模型
llm = LLM(
    model="meta-llama/Llama-2-7b-chat-hf",
    tensor_parallel_size=1,  # GPU 数量
    dtype="half"  # 使用半精度以节省内存
)

# 批量输入
prompts = [
    "解释什么是人工智能",
    "Python 编程的优势是什么？",
    "描述机器学习的基本概念"
]

# 采样参数
sampling_params = SamplingParams(
    temperature=0.7,
    top_p=0.9,
    max_tokens=256
)

# 批量生成
outputs = llm.generate(prompts, sampling_params)

for i, output in enumerate(outputs):
    print(f"\n=== 输出 {i+1} ===")
    print(f"输入: {output.prompt}")
    print(f"输出: {output.outputs[0].text}")</code></pre>
        </section>

        <section id="offline-inference">
            <h2>4. 离线推理</h2>
            
            <h3>4.1 加载本地模型</h3>
            <pre><code>from vllm import LLM, SamplingParams

# 从本地路径加载模型
llm = LLM(model="/path/to/your/model")

# 或从 Hugging Face Hub 加载
llm = LLM(model="microsoft/DialoGPT-medium")</code></pre>

            <h3>4.2 内存优化配置</h3>
            <pre><code># 内存优化配置
llm = LLM(
    model="meta-llama/Llama-2-13b-chat-hf",
    tensor_parallel_size=2,  # 使用 2 个 GPU
    dtype="float16",         # 使用 FP16 精度
    max_model_len=2048,      # 限制最大序列长度
    gpu_memory_utilization=0.9,  # GPU 内存使用率
    swap_space=4,            # 4GB swap 空间
)</code></pre>

            <h3>4.3 高级采样配置</h3>
            <pre><code>from vllm import SamplingParams

# 多种采样策略
sampling_params = SamplingParams(
    n=3,                    # 生成 3 个候选
    temperature=0.8,        # 温度参数
    top_p=0.95,            # nucleus 采样
    top_k=50,              # top-k 采样
    frequency_penalty=0.1,  # 频率惩罚
    presence_penalty=0.1,   # 存在惩罚
    max_tokens=512,        # 最大生成长度
    stop=["</s>", "\n\n"]  # 停止标记
)

outputs = llm.generate(["讲一个有趣的故事"], sampling_params)

# 输出所有候选
for i, completion in enumerate(outputs[0].outputs):
    print(f"\n候选 {i+1}: {completion.text}")</code></pre>
        </section>

        <section id="api-server">
            <h2>5. API 服务器部署</h2>
            
            <h3>5.1 启动 OpenAI 兼容服务器</h3>
            <pre><code># 命令行启动服务器
python -m vllm.entrypoints.openai.api_server \
    --model microsoft/DialoGPT-medium \
    --host 0.0.0.0 \
    --port 8000</code></pre>

            <h3>5.2 高级服务器配置</h3>
            <pre><code># 多 GPU 部署
python -m vllm.entrypoints.openai.api_server \
    --model meta-llama/Llama-2-7b-chat-hf \
    --tensor-parallel-size 4 \
    --host 0.0.0.0 \
    --port 8000 \
    --dtype float16 \
    --max-model-len 4096</code></pre>

            <h3>5.3 客户端调用示例</h3>
            <pre><code>import openai

# 配置客户端
openai.api_key = "EMPTY"
openai.api_base = "http://localhost:8000/v1"

# Chat Completions API
response = openai.ChatCompletion.create(
    model="meta-llama/Llama-2-7b-chat-hf",
    messages=[
        {"role": "system", "content": "你是一个有用的助手。"},
        {"role": "user", "content": "解释量子计算的基本原理"}
    ],
    temperature=0.7,
    max_tokens=512
)

print(response.choices[0].message.content)</code></pre>

            <h3>5.4 curl 请求示例</h3>
            <pre><code># Chat Completions
curl http://localhost:8000/v1/chat/completions \
    -H "Content-Type: application/json" \
    -d '{
        "model": "meta-llama/Llama-2-7b-chat-hf",
        "messages": [
            {"role": "user", "content": "Hello!"}
        ],
        "temperature": 0.7,
        "max_tokens": 50
    }'

# Completions
curl http://localhost:8000/v1/completions \
    -H "Content-Type: application/json" \
    -d '{
        "model": "meta-llama/Llama-2-7b-chat-hf",
        "prompt": "San Francisco is a",
        "max_tokens": 50,
        "temperature": 0
    }'</code></pre>
        </section>

        <section id="optimization">
            <h2>6. 性能优化</h2>
            
            <h3>6.1 内存优化</h3>
            <table>
                <thead>
                    <tr>
                        <th>参数</th>
                        <th>说明</th>
                        <th>推荐值</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>gpu_memory_utilization</code></td>
                        <td>GPU 内存使用比例</td>
                        <td>0.85-0.95</td>
                    </tr>
                    <tr>
                        <td><code>max_model_len</code></td>
                        <td>最大序列长度</td>
                        <td>根据需求设置</td>
                    </tr>
                    <tr>
                        <td><code>dtype</code></td>
                        <td>数据类型</td>
                        <td>float16/bfloat16</td>
                    </tr>
                    <tr>
                        <td><code>swap_space</code></td>
                        <td>CPU 交换空间 (GB)</td>
                        <td>4-8</td>
                    </tr>
                </tbody>
            </table>

            <h3>6.2 性能调优示例</h3>
            <pre><code># 高性能配置
llm = LLM(
    model="meta-llama/Llama-2-70b-chat-hf",
    tensor_parallel_size=8,        # 8 GPU 并行
    dtype="bfloat16",              # 使用 bfloat16
    gpu_memory_utilization=0.95,   # 高内存利用率
    max_model_len=4096,            # 合适的序列长度
    swap_space=8,                  # 8GB 交换空间
    enforce_eager=False,           # 启用 CUDA graph
)</code></pre>

            <h3>6.3 批处理优化</h3>
            <pre><code># 批处理策略
import asyncio
from vllm import AsyncLLMEngine, AsyncEngineArgs, SamplingParams

async def batch_generate():
    # 异步引擎配置
    engine_args = AsyncEngineArgs(
        model="meta-llama/Llama-2-7b-chat-hf",
        tensor_parallel_size=1,
        dtype="float16"
    )
    
    engine = AsyncLLMEngine.from_engine_args(engine_args)
    
    # 批量请求
    prompts = ["Hello"] * 100  # 100 个相同请求
    sampling_params = SamplingParams(temperature=0.8, max_tokens=64)
    
    # 异步生成
    results = []
    for prompt in prompts:
        result = engine.generate(prompt, sampling_params, request_id=f"req_{len(results)}")
        results.append(result)
    
    # 等待所有结果
    outputs = await asyncio.gather(*results)
    return outputs

# 运行异步批处理
outputs = asyncio.run(batch_generate())</code></pre>
        </section>

        <section id="distributed">
            <h2>7. 分布式推理</h2>
            
            <h3>7.1 多 GPU 配置</h3>
            <pre><code># 张量并行（单节点多 GPU）
llm = LLM(
    model="meta-llama/Llama-2-70b-chat-hf",
    tensor_parallel_size=4,  # 使用 4 个 GPU
    dtype="float16"
)

# 检查 GPU 使用情况
import torch
print(f"可用 GPU 数量: {torch.cuda.device_count()}")
for i in range(torch.cuda.device_count()):
    print(f"GPU {i}: {torch.cuda.get_device_name(i)}")</code></pre>

            <h3>7.2 多节点部署</h3>
            <pre><code># 节点 0 (主节点)
python -m vllm.entrypoints.openai.api_server \
    --model meta-llama/Llama-2-70b-chat-hf \
    --tensor-parallel-size 8 \
    --pipeline-parallel-size 2 \
    --distributed-executor-backend ray \
    --host 0.0.0.0 \
    --port 8000

# 节点 1 (工作节点)
ray start --address="主节点IP:10001"</code></pre>

            <h3>7.3 Ray 集群配置</h3>
            <pre><code># cluster.yaml
cluster_name: vllm_cluster

max_workers: 4

head_node:
    instance_type: g4dn.12xlarge
    resources: {"GPU": 4}

worker_nodes:
    instance_type: g4dn.12xlarge
    min_workers: 0
    max_workers: 3
    resources: {"GPU": 4}

# 启动集群
ray up cluster.yaml

# 部署服务
ray exec cluster.yaml "python -m vllm.entrypoints.openai.api_server \
    --model meta-llama/Llama-2-70b-chat-hf \
    --tensor-parallel-size 16"</code></pre>
        </section>

        <section id="best-practices">
            <h2>8. 最佳实践</h2>
            
            <h3>8.1 模型选择指南</h3>
            <div class="info">
                <strong>💡 选择建议：</strong>
                <ul>
                    <li><strong>7B 模型</strong>：适合单 GPU (24GB VRAM) 部署</li>
                    <li><strong>13B 模型</strong>：需要 2-4 个 GPU</li>
                    <li><strong>70B 模型</strong>：需要 4-8 个 GPU</li>
                    <li><strong>考虑量化</strong>：使用 AWQ/GPTQ 量化可减少内存需求</li>
                </ul>
            </div>

            <h3>8.2 生产环境部署清单</h3>
            <ul>
                <li>✅ 设置适当的 <code>max_model_len</code> 避免 OOM</li>
                <li>✅ 使用 <code>dtype="float16"</code> 或 <code>"bfloat16"</code></li>
                <li>✅ 配置健康检查和监控</li>
                <li>✅ 设置请求超时和速率限制</li>
                <li>✅ 使用负载均衡器分发请求</li>
                <li>✅ 定期备份模型和配置</li>
            </ul>

            <h3>8.3 监控和日志</h3>
            <pre><code># 启用详细日志
import logging
logging.basicConfig(level=logging.INFO)

# 性能监控
from vllm import LLM
import time
import psutil

llm = LLM(model="microsoft/DialoGPT-medium")

def monitor_inference(prompts):
    start_time = time.time()
    memory_before = psutil.virtual_memory().used / 1024**3
    
    outputs = llm.generate(prompts, SamplingParams(max_tokens=100))
    
    end_time = time.time()
    memory_after = psutil.virtual_memory().used / 1024**3
    
    print(f"推理时间: {end_time - start_time:.2f}秒")
    print(f"内存使用: {memory_after - memory_before:.2f}GB")
    print(f"吞吐量: {len(prompts)/(end_time - start_time):.2f} requests/sec")
    
    return outputs</code></pre>
        </section>

        <section id="troubleshooting">
            <h2>9. 常见问题</h2>
            
            <h3>9.1 内存不足 (OOM)</h3>
            <div class="warning">
                <strong>问题：</strong> CUDA out of memory
                <br><strong>解决方案：</strong>
                <ul>
                    <li>减少 <code>max_model_len</code></li>
                    <li>降低 <code>gpu_memory_utilization</code></li>
                    <li>使用更小的批次大小</li>
                    <li>启用 CPU offloading</li>
                </ul>
            </div>

            <h3>9.2 性能问题</h3>
            <div class="info">
                <strong>症状：</strong> 推理速度慢
                <br><strong>优化方案：</strong>
                <ul>
                    <li>检查 GPU 利用率</li>
                    <li>调整 <code>tensor_parallel_size</code></li>
                    <li>使用合适的数据类型 (float16/bfloat16)</li>
                    <li>优化批处理大小</li>
                </ul>
            </div>

            <h3>9.3 常见错误码</h3>
            <table>
                <thead>
                    <tr>
                        <th>错误</th>
                        <th>原因</th>
                        <th>解决方法</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>ImportError</td>
                        <td>依赖缺失</td>
                        <td>重新安装 vLLM 和依赖</td>
                    </tr>
                    <tr>
                        <td>Model not found</td>
                        <td>模型路径错误</td>
                        <td>检查模型路径或 HF token</td>
                    </tr>
                    <tr>
                        <td>CUDA version mismatch</td>
                        <td>CUDA 版本不兼容</td>
                        <td>安装匹配的 PyTorch 版本</td>
                    </tr>
                </tbody>
            </table>

            <h3>9.4 调试工具</h3>
            <pre><code># 检查环境
python -c "import vllm; print(vllm.__version__)"
python -c "import torch; print(torch.cuda.is_available())"
nvidia-smi

# 测试基本功能
from vllm import LLM, SamplingParams
llm = LLM(model="microsoft/DialoGPT-medium", dtype="float16")
outputs = llm.generate(["Hello"], SamplingParams(max_tokens=10))
print(outputs[0].outputs[0].text)</code></pre>
        </section>

        <div class="success">
            <h3>🎉 恭喜！</h3>
            <p>你已经掌握了 vLLM 的基本使用方法。继续探索更多高级功能，构建强大的 AI 应用！</p>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <a href="index.html" class="back-button">← 返回主页</a>
        </div>
    </div>
</body>
</html>