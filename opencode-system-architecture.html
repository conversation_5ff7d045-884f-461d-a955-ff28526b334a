<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenCode 系统架构图和流程图</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .diagram-section {
            margin-bottom: 50px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        }
        
        .diagram-title {
            font-size: 1.8em;
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        
        .diagram-description {
            color: #666;
            margin-bottom: 25px;
            line-height: 1.6;
            font-size: 1.1em;
        }
        
        .svg-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        svg {
            width: 100%;
            height: auto;
            min-height: 400px;
        }
        
        .legend {
            background: #ecf0f1;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .legend h4 {
            margin: 0 0 15px 0;
            color: #2c3e50;
        }
        
        .legend-item {
            display: inline-block;
            margin: 5px 15px 5px 0;
            font-size: 0.9em;
        }
        
        .legend-color {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 3px;
            margin-right: 8px;
            vertical-align: middle;
        }
        
        .nav-tabs {
            display: flex;
            background: #ecf0f1;
            border-radius: 8px;
            padding: 5px;
            margin-bottom: 20px;
        }
        
        .nav-tab {
            flex: 1;
            padding: 12px 20px;
            text-align: center;
            background: transparent;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1em;
            color: #666;
        }
        
        .nav-tab.active {
            background: #3498db;
            color: white;
            box-shadow: 0 2px 5px rgba(52, 152, 219, 0.3);
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>OpenCode 系统架构图和流程图</h1>
            <p>基于 Go 语言的终端 AI 编程助手系统架构可视化</p>
        </div>
        
        <div class="content">
            <!-- 导航标签 -->
            <div class="nav-tabs">
                <button class="nav-tab active" onclick="showTab('overview')">系统总览</button>
                <button class="nav-tab" onclick="showTab('dataflow')">数据流程</button>
                <button class="nav-tab" onclick="showTab('components')">组件交互</button>
                <button class="nav-tab" onclick="showTab('tools')">工具系统</button>
                <button class="nav-tab" onclick="showTab('sequence')">执行序列</button>
            </div>

            <!-- 系统总览架构图 -->
            <div id="overview" class="tab-content active">
                <div class="diagram-section">
                    <h2 class="diagram-title">🏗️ 系统总体架构</h2>
                    <p class="diagram-description">
                        展示 OpenCode 系统的整体架构，包括用户界面层、应用层、业务逻辑层和数据层的完整结构关系。
                    </p>
                    <div class="svg-container">
                        <svg viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
                            <!-- 背景渐变定义 -->
                            <defs>
                                <linearGradient id="uiGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="appGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#e1bee7;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="businessGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="dataGrad" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#ffe0b2;stop-opacity:1" />
                                </linearGradient>
                            </defs>
                            
                            <!-- 用户界面层 -->
                            <rect x="50" y="50" width="1100" height="120" rx="10" fill="url(#uiGrad)" stroke="#2196f3" stroke-width="2"/>
                            <text x="600" y="75" text-anchor="middle" font-size="18" font-weight="bold" fill="#1976d2">用户界面层 (TUI)</text>
                            
                            <rect x="80" y="90" width="150" height="60" rx="5" fill="#e3f2fd" stroke="#2196f3"/>
                            <text x="155" y="115" text-anchor="middle" font-size="12" fill="#1976d2">聊天页面</text>
                            <text x="155" y="130" text-anchor="middle" font-size="12" fill="#1976d2">ChatPage</text>
                            
                            <rect x="250" y="90" width="150" height="60" rx="5" fill="#e3f2fd" stroke="#2196f3"/>
                            <text x="325" y="115" text-anchor="middle" font-size="12" fill="#1976d2">日志页面</text>
                            <text x="325" y="130" text-anchor="middle" font-size="12" fill="#1976d2">LogsPage</text>
                            
                            <rect x="420" y="90" width="150" height="60" rx="5" fill="#e3f2fd" stroke="#2196f3"/>
                            <text x="495" y="115" text-anchor="middle" font-size="12" fill="#1976d2">对话框组件</text>
                            <text x="495" y="130" text-anchor="middle" font-size="12" fill="#1976d2">Dialogs</text>
                            
                            <rect x="590" y="90" width="150" height="60" rx="5" fill="#e3f2fd" stroke="#2196f3"/>
                            <text x="665" y="115" text-anchor="middle" font-size="12" fill="#1976d2">编辑器组件</text>
                            <text x="665" y="130" text-anchor="middle" font-size="12" fill="#1976d2">Editor</text>
                            
                            <!-- 应用层 -->
                            <rect x="50" y="200" width="1100" height="120" rx="10" fill="url(#appGrad)" stroke="#9c27b0" stroke-width="2"/>
                            <text x="600" y="225" text-anchor="middle" font-size="18" font-weight="bold" fill="#7b1fa2">应用层 (App)</text>
                            
                            <rect x="80" y="240" width="150" height="60" rx="5" fill="#f3e5f5" stroke="#9c27b0"/>
                            <text x="155" y="265" text-anchor="middle" font-size="12" fill="#7b1fa2">应用服务</text>
                            <text x="155" y="280" text-anchor="middle" font-size="12" fill="#7b1fa2">AppService</text>
                            
                            <rect x="250" y="240" width="150" height="60" rx="5" fill="#f3e5f5" stroke="#9c27b0"/>
                            <text x="325" y="265" text-anchor="middle" font-size="12" fill="#7b1fa2">会话管理器</text>
                            <text x="325" y="280" text-anchor="middle" font-size="12" fill="#7b1fa2">SessionMgr</text>
                            
                            <rect x="420" y="240" width="150" height="60" rx="5" fill="#f3e5f5" stroke="#9c27b0"/>
                            <text x="495" y="265" text-anchor="middle" font-size="12" fill="#7b1fa2">配置管理器</text>
                            <text x="495" y="280" text-anchor="middle" font-size="12" fill="#7b1fa2">ConfigMgr</text>
                            
                            <rect x="590" y="240" width="150" height="60" rx="5" fill="#f3e5f5" stroke="#9c27b0"/>
                            <text x="665" y="265" text-anchor="middle" font-size="12" fill="#7b1fa2">LSP管理器</text>
                            <text x="665" y="280" text-anchor="middle" font-size="12" fill="#7b1fa2">LSPMgr</text>
                            
                            <!-- 业务逻辑层 -->
                            <rect x="50" y="350" width="1100" height="200" rx="10" fill="url(#businessGrad)" stroke="#4caf50" stroke-width="2"/>
                            <text x="600" y="375" text-anchor="middle" font-size="18" font-weight="bold" fill="#388e3c">业务逻辑层</text>
                            
                            <!-- 核心服务 -->
                            <rect x="80" y="390" width="200" height="140" rx="5" fill="#e8f5e8" stroke="#4caf50"/>
                            <text x="180" y="410" text-anchor="middle" font-size="14" font-weight="bold" fill="#388e3c">核心服务</text>
                            <rect x="90" y="420" width="80" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="130" y="435" text-anchor="middle" font-size="10" fill="#2e7d32">Sessions</text>
                            <rect x="180" y="420" width="80" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="220" y="435" text-anchor="middle" font-size="10" fill="#2e7d32">Messages</text>
                            <rect x="90" y="450" width="80" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="130" y="465" text-anchor="middle" font-size="10" fill="#2e7d32">Permissions</text>
                            <rect x="180" y="450" width="80" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="220" y="465" text-anchor="middle" font-size="10" fill="#2e7d32">History</text>
                            
                            <!-- AI代理系统 -->
                            <rect x="300" y="390" width="200" height="140" rx="5" fill="#e8f5e8" stroke="#4caf50"/>
                            <text x="400" y="410" text-anchor="middle" font-size="14" font-weight="bold" fill="#388e3c">AI代理系统</text>
                            <rect x="310" y="420" width="80" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="350" y="435" text-anchor="middle" font-size="10" fill="#2e7d32">CoderAgent</text>
                            <rect x="400" y="420" width="80" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="440" y="435" text-anchor="middle" font-size="10" fill="#2e7d32">TaskAgent</text>
                            <rect x="310" y="450" width="80" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="350" y="465" text-anchor="middle" font-size="10" fill="#2e7d32">TitleAgent</text>
                            <rect x="400" y="450" width="80" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="440" y="465" text-anchor="middle" font-size="10" fill="#2e7d32">Summarizer</text>
                            
                            <!-- 工具系统 -->
                            <rect x="520" y="390" width="300" height="140" rx="5" fill="#e8f5e8" stroke="#4caf50"/>
                            <text x="670" y="410" text-anchor="middle" font-size="14" font-weight="bold" fill="#388e3c">工具系统</text>
                            <rect x="530" y="420" width="60" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="560" y="435" text-anchor="middle" font-size="9" fill="#2e7d32">BashTool</text>
                            <rect x="600" y="420" width="60" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="630" y="435" text-anchor="middle" font-size="9" fill="#2e7d32">EditTool</text>
                            <rect x="670" y="420" width="60" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="700" y="435" text-anchor="middle" font-size="9" fill="#2e7d32">FileTool</text>
                            <rect x="740" y="420" width="60" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="770" y="435" text-anchor="middle" font-size="9" fill="#2e7d32">SearchTool</text>
                            <rect x="530" y="450" width="60" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="560" y="465" text-anchor="middle" font-size="9" fill="#2e7d32">LSPTool</text>
                            <rect x="600" y="450" width="60" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="630" y="465" text-anchor="middle" font-size="9" fill="#2e7d32">WebTool</text>
                            <rect x="670" y="450" width="60" height="25" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="700" y="465" text-anchor="middle" font-size="9" fill="#2e7d32">MCPTool</text>
                            
                            <!-- 事件系统 -->
                            <rect x="840" y="390" width="150" height="140" rx="5" fill="#e8f5e8" stroke="#4caf50"/>
                            <text x="915" y="410" text-anchor="middle" font-size="14" font-weight="bold" fill="#388e3c">事件系统</text>
                            <rect x="860" y="430" width="110" height="30" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="915" y="450" text-anchor="middle" font-size="12" fill="#2e7d32">PubSub</text>
                            <rect x="860" y="470" width="110" height="30" rx="3" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="915" y="490" text-anchor="middle" font-size="12" fill="#2e7d32">Events</text>
                            
                            <!-- 数据层 -->
                            <rect x="50" y="580" width="1100" height="170" rx="10" fill="url(#dataGrad)" stroke="#ff9800" stroke-width="2"/>
                            <text x="600" y="605" text-anchor="middle" font-size="18" font-weight="bold" fill="#f57c00">数据层</text>
                            
                            <!-- 本地存储 -->
                            <rect x="80" y="620" width="200" height="110" rx="5" fill="#fff3e0" stroke="#ff9800"/>
                            <text x="180" y="640" text-anchor="middle" font-size="14" font-weight="bold" fill="#f57c00">本地存储</text>
                            <ellipse cx="130" cy="670" rx="40" ry="20" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="130" y="675" text-anchor="middle" font-size="10" fill="#e65100">SQLite</text>
                            <rect x="190" y="655" width="70" height="30" rx="3" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="225" y="675" text-anchor="middle" font-size="10" fill="#e65100">FileSystem</text>
                            
                            <!-- 外部服务 -->
                            <rect x="300" y="620" width="500" height="110" rx="5" fill="#fff3e0" stroke="#ff9800"/>
                            <text x="550" y="640" text-anchor="middle" font-size="14" font-weight="bold" fill="#f57c00">外部服务</text>
                            <rect x="320" y="655" width="70" height="25" rx="3" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="355" y="670" text-anchor="middle" font-size="9" fill="#e65100">OpenAI</text>
                            <rect x="400" y="655" width="70" height="25" rx="3" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="435" y="670" text-anchor="middle" font-size="9" fill="#e65100">Anthropic</text>
                            <rect x="480" y="655" width="70" height="25" rx="3" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="515" y="670" text-anchor="middle" font-size="9" fill="#e65100">Google</text>
                            <rect x="560" y="655" width="70" height="25" rx="3" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="595" y="670" text-anchor="middle" font-size="9" fill="#e65100">Azure</text>
                            <rect x="640" y="655" width="70" height="25" rx="3" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="675" y="670" text-anchor="middle" font-size="9" fill="#e65100">LSPServers</text>
                            <rect x="720" y="655" width="70" height="25" rx="3" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="755" y="670" text-anchor="middle" font-size="9" fill="#e65100">MCPServers</text>
                            
                            <!-- 连接线 -->
                            <!-- UI到App层 -->
                            <line x1="155" y1="150" x2="155" y2="240" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="325" y1="150" x2="325" y2="240" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- App到Business层 -->
                            <line x1="155" y1="300" x2="180" y2="390" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="325" y1="300" x2="400" y2="390" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- Business到Data层 -->
                            <line x1="180" y1="530" x2="180" y2="620" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                            <line x1="400" y1="530" x2="550" y2="620" stroke="#666" stroke-width="2" marker-end="url(#arrowhead)"/>
                            
                            <!-- 箭头标记定义 -->
                            <defs>
                                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                                </marker>
                            </defs>
                        </svg>
                    </div>
                    
                    <div class="legend">
                        <h4>图例说明</h4>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #e3f2fd;"></span>
                            用户界面层 - 终端用户交互界面
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #f3e5f5;"></span>
                            应用层 - 核心应用服务管理
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #e8f5e8;"></span>
                            业务逻辑层 - AI代理和工具系统
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #fff3e0;"></span>
                            数据层 - 存储和外部服务
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据流程图 -->
            <div id="dataflow" class="tab-content">
                <div class="diagram-section">
                    <h2 class="diagram-title">🔄 数据流程架构</h2>
                    <p class="diagram-description">
                        展示用户输入到AI响应的完整数据流转过程，包括权限控制、工具调用和结果返回的详细流程。
                    </p>
                    <div class="svg-container">
                        <svg viewBox="0 0 1200 600" xmlns="http://www.w3.org/2000/svg">
                            <!-- 流程节点 -->
                            <rect x="50" y="50" width="120" height="60" rx="30" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>
                            <text x="110" y="85" text-anchor="middle" font-size="14" font-weight="bold" fill="#1976d2">用户输入</text>
                            
                            <rect x="220" y="50" width="120" height="60" rx="10" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2"/>
                            <text x="280" y="75" text-anchor="middle" font-size="12" fill="#7b1fa2">终端界面</text>
                            <text x="280" y="90" text-anchor="middle" font-size="12" fill="#7b1fa2">处理</text>
                            
                            <rect x="390" y="50" width="120" height="60" rx="10" fill="#e8f5e8" stroke="#4caf50" stroke-width="2"/>
                            <text x="450" y="75" text-anchor="middle" font-size="12" fill="#388e3c">应用服务</text>
                            <text x="450" y="90" text-anchor="middle" font-size="12" fill="#388e3c">调度</text>
                            
                            <rect x="560" y="50" width="120" height="60" rx="10" fill="#fff3e0" stroke="#ff9800" stroke-width="2"/>
                            <text x="620" y="75" text-anchor="middle" font-size="12" fill="#f57c00">AI代理</text>
                            <text x="620" y="90" text-anchor="middle" font-size="12" fill="#f57c00">处理</text>
                            
                            <rect x="730" y="50" width="120" height="60" rx="10" fill="#ffebee" stroke="#f44336" stroke-width="2"/>
                            <text x="790" y="75" text-anchor="middle" font-size="12" fill="#d32f2f">LLM</text>
                            <text x="790" y="90" text-anchor="middle" font-size="12" fill="#d32f2f">推理</text>
                            
                            <!-- 工具调用分支 -->
                            <rect x="900" y="50" width="120" height="60" rx="10" fill="#e1f5fe" stroke="#00bcd4" stroke-width="2"/>
                            <text x="960" y="75" text-anchor="middle" font-size="12" fill="#0097a7">工具调用</text>
                            <text x="960" y="90" text-anchor="middle" font-size="12" fill="#0097a7">判断</text>
                            
                            <!-- 权限控制 -->
                            <rect x="730" y="180" width="120" height="60" rx="10" fill="#fce4ec" stroke="#e91e63" stroke-width="2"/>
                            <text x="790" y="205" text-anchor="middle" font-size="12" fill="#c2185b">权限检查</text>
                            <text x="790" y="220" text-anchor="middle" font-size="12" fill="#c2185b">系统</text>
                            
                            <rect x="560" y="180" width="120" height="60" rx="10" fill="#f1f8e9" stroke="#8bc34a" stroke-width="2"/>
                            <text x="620" y="205" text-anchor="middle" font-size="12" fill="#689f38">工具执行</text>
                            <text x="620" y="220" text-anchor="middle" font-size="12" fill="#689f38">系统</text>
                            
                            <!-- 数据存储 -->
                            <ellipse cx="280" cy="350" rx="60" ry="30" fill="#e8eaf6" stroke="#3f51b5" stroke-width="2"/>
                            <text x="280" y="355" text-anchor="middle" font-size="12" fill="#303f9f">SQLite</text>
                            
                            <rect x="450" y="320" width="120" height="60" rx="10" fill="#e0f2f1" stroke="#009688" stroke-width="2"/>
                            <text x="510" y="345" text-anchor="middle" font-size="12" fill="#00695c">文件系统</text>
                            <text x="510" y="360" text-anchor="middle" font-size="12" fill="#00695c">操作</text>
                            
                            <!-- 响应流 -->
                            <rect x="900" y="320" width="120" height="60" rx="10" fill="#fff8e1" stroke="#ffc107" stroke-width="2"/>
                            <text x="960" y="345" text-anchor="middle" font-size="12" fill="#ff8f00">结果整合</text>
                            <text x="960" y="360" text-anchor="middle" font-size="12" fill="#ff8f00">返回</text>
                            
                            <rect x="730" y="450" width="120" height="60" rx="10" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2"/>
                            <text x="790" y="475" text-anchor="middle" font-size="12" fill="#7b1fa2">界面更新</text>
                            <text x="790" y="490" text-anchor="middle" font-size="12" fill="#7b1fa2">显示</text>
                            
                            <rect x="50" y="450" width="120" height="60" rx="30" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>
                            <text x="110" y="485" text-anchor="middle" font-size="14" font-weight="bold" fill="#1976d2">用户查看</text>
                            
                            <!-- 连接线和箭头 -->
                            <defs>
                                <marker id="arrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#666"/>
                                </marker>
                            </defs>
                            
                            <!-- 主流程线 -->
                            <line x1="170" y1="80" x2="220" y2="80" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                            <line x1="340" y1="80" x2="390" y2="80" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                            <line x1="510" y1="80" x2="560" y2="80" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                            <line x1="680" y1="80" x2="730" y2="80" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                            <line x1="850" y1="80" x2="900" y2="80" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                            
                            <!-- 工具调用分支 -->
                            <line x1="960" y1="110" x2="960" y2="140" stroke="#666" stroke-width="2"/>
                            <line x1="960" y1="140" x2="790" y2="140" stroke="#666" stroke-width="2"/>
                            <line x1="790" y1="140" x2="790" y2="180" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                            
                            <line x1="730" y1="210" x2="680" y2="210" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                            
                            <!-- 存储操作 -->
                            <line x1="620" y1="240" x2="620" y2="280" stroke="#666" stroke-width="2"/>
                            <line x1="620" y1="280" x2="510" y2="280" stroke="#666" stroke-width="2"/>
                            <line x1="510" y1="280" x2="510" y2="320" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                            
                            <line x1="450" y1="350" x2="340" y2="350" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                            
                            <!-- 结果返回 -->
                            <line x1="680" y1="210" x2="960" y2="210" stroke="#666" stroke-width="2"/>
                            <line x1="960" y1="210" x2="960" y2="320" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                            
                            <line x1="900" y1="350" x2="850" y2="350" stroke="#666" stroke-width="2"/>
                            <line x1="850" y1="350" x2="850" y2="480" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                            
                            <line x1="730" y1="480" x2="170" y2="480" stroke="#666" stroke-width="2" marker-end="url(#arrow)"/>
                            
                            <!-- 流程标签 -->
                            <text x="195" y="70" text-anchor="middle" font-size="10" fill="#666">1</text>
                            <text x="365" y="70" text-anchor="middle" font-size="10" fill="#666">2</text>
                            <text x="535" y="70" text-anchor="middle" font-size="10" fill="#666">3</text>
                            <text x="705" y="70" text-anchor="middle" font-size="10" fill="#666">4</text>
                            <text x="875" y="70" text-anchor="middle" font-size="10" fill="#666">5</text>
                            
                            <text x="875" y="160" text-anchor="middle" font-size="10" fill="#666">6</text>
                            <text x="705" y="200" text-anchor="middle" font-size="10" fill="#666">7</text>
                            <text x="650" y="200" text-anchor="middle" font-size="10" fill="#666">8</text>
                            <text x="875" y="340" text-anchor="middle" font-size="10" fill="#666">9</text>
                            <text x="705" y="470" text-anchor="middle" font-size="10" fill="#666">10</text>
                        </svg>
                    </div>
                    
                    <div class="legend">
                        <h4>流程步骤说明</h4>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; font-size: 0.9em;">
                            <div>1. 用户在终端输入命令或问题</div>
                            <div>2. TUI组件处理用户输入</div>
                            <div>3. 应用服务接收并调度请求</div>
                            <div>4. AI代理分析用户意图</div>
                            <div>5. LLM进行推理和决策</div>
                            <div>6. 判断是否需要调用工具</div>
                            <div>7. 权限系统检查操作权限</div>
                            <div>8. 执行具体的工具操作</div>
                            <div>9. 整合结果并准备响应</div>
                            <div>10. 更新界面显示结果给用户</div>
                        </div>
                    </div>
                </div>
            </div>    
        <!-- 组件交互图 -->
            <div id="components" class="tab-content">
                <div class="diagram-section">
                    <h2 class="diagram-title">🔗 组件交互架构</h2>
                    <p class="diagram-description">
                        展示OpenCode各核心组件之间的交互关系，包括服务间通信、事件传递和数据共享机制。
                    </p>
                    <div class="svg-container">
                        <svg viewBox="0 0 1200 700" xmlns="http://www.w3.org/2000/svg">
                            <!-- 中心应用服务 -->
                            <circle cx="600" cy="350" r="80" fill="#ff9800" stroke="#f57c00" stroke-width="3"/>
                            <text x="600" y="345" text-anchor="middle" font-size="14" font-weight="bold" fill="white">应用服务</text>
                            <text x="600" y="360" text-anchor="middle" font-size="12" fill="white">App</text>
                            
                            <!-- 核心服务组件 -->
                            <rect x="100" y="100" width="120" height="80" rx="10" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                            <text x="160" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="white">会话服务</text>
                            <text x="160" y="145" text-anchor="middle" font-size="10" fill="white">Session</text>
                            <text x="160" y="160" text-anchor="middle" font-size="10" fill="white">Management</text>
                            
                            <rect x="280" y="100" width="120" height="80" rx="10" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                            <text x="340" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="white">消息服务</text>
                            <text x="340" y="145" text-anchor="middle" font-size="10" fill="white">Message</text>
                            <text x="340" y="160" text-anchor="middle" font-size="10" fill="white">Service</text>
                            
                            <rect x="800" y="100" width="120" height="80" rx="10" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                            <text x="860" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="white">权限服务</text>
                            <text x="860" y="145" text-anchor="middle" font-size="10" fill="white">Permission</text>
                            <text x="860" y="160" text-anchor="middle" font-size="10" fill="white">Service</text>
                            
                            <rect x="980" y="100" width="120" height="80" rx="10" fill="#4caf50" stroke="#388e3c" stroke-width="2"/>
                            <text x="1040" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="white">历史服务</text>
                            <text x="1040" y="145" text-anchor="middle" font-size="10" fill="white">History</text>
                            <text x="1040" y="160" text-anchor="middle" font-size="10" fill="white">Service</text>
                            
                            <!-- AI系统 -->
                            <rect x="100" y="520" width="120" height="80" rx="10" fill="#9c27b0" stroke="#7b1fa2" stroke-width="2"/>
                            <text x="160" y="550" text-anchor="middle" font-size="12" font-weight="bold" fill="white">代理系统</text>
                            <text x="160" y="565" text-anchor="middle" font-size="10" fill="white">Agent</text>
                            <text x="160" y="580" text-anchor="middle" font-size="10" fill="white">System</text>
                            
                            <rect x="280" y="520" width="120" height="80" rx="10" fill="#9c27b0" stroke="#7b1fa2" stroke-width="2"/>
                            <text x="340" y="550" text-anchor="middle" font-size="12" font-weight="bold" fill="white">LLM提供商</text>
                            <text x="340" y="565" text-anchor="middle" font-size="10" fill="white">LLM</text>
                            <text x="340" y="580" text-anchor="middle" font-size="10" fill="white">Providers</text>
                            
                            <rect x="460" y="520" width="120" height="80" rx="10" fill="#9c27b0" stroke="#7b1fa2" stroke-width="2"/>
                            <text x="520" y="550" text-anchor="middle" font-size="12" font-weight="bold" fill="white">工具系统</text>
                            <text x="520" y="565" text-anchor="middle" font-size="10" fill="white">Tools</text>
                            <text x="520" y="580" text-anchor="middle" font-size="10" fill="white">System</text>
                            
                            <!-- 外部集成 -->
                            <rect x="800" y="520" width="120" height="80" rx="10" fill="#00bcd4" stroke="#0097a7" stroke-width="2"/>
                            <text x="860" y="550" text-anchor="middle" font-size="12" font-weight="bold" fill="white">LSP客户端</text>
                            <text x="860" y="565" text-anchor="middle" font-size="10" fill="white">LSP</text>
                            <text x="860" y="580" text-anchor="middle" font-size="10" fill="white">Client</text>
                            
                            <rect x="980" y="520" width="120" height="80" rx="10" fill="#00bcd4" stroke="#0097a7" stroke-width="2"/>
                            <text x="1040" y="550" text-anchor="middle" font-size="12" font-weight="bold" fill="white">MCP工具</text>
                            <text x="1040" y="565" text-anchor="middle" font-size="10" fill="white">MCP</text>
                            <text x="1040" y="580" text-anchor="middle" font-size="10" fill="white">Tools</text>
                            
                            <!-- UI系统 -->
                            <rect x="460" y="100" width="120" height="80" rx="10" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                            <text x="520" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="white">终端界面</text>
                            <text x="520" y="145" text-anchor="middle" font-size="10" fill="white">TUI</text>
                            <text x="520" y="160" text-anchor="middle" font-size="10" fill="white">System</text>
                            
                            <rect x="640" y="100" width="120" height="80" rx="10" fill="#2196f3" stroke="#1976d2" stroke-width="2"/>
                            <text x="700" y="130" text-anchor="middle" font-size="12" font-weight="bold" fill="white">对话框</text>
                            <text x="700" y="145" text-anchor="middle" font-size="10" fill="white">Dialogs</text>
                            <text x="700" y="160" text-anchor="middle" font-size="10" fill="white">Components</text>
                            
                            <!-- 数据存储 -->
                            <ellipse cx="160" cy="350" rx="60" ry="40" fill="#607d8b" stroke="#455a64" stroke-width="2"/>
                            <text x="160" y="350" text-anchor="middle" font-size="12" font-weight="bold" fill="white">数据库</text>
                            <text x="160" y="365" text-anchor="middle" font-size="10" fill="white">SQLite</text>
                            
                            <rect x="280" y="320" width="120" height="60" rx="10" fill="#607d8b" stroke="#455a64" stroke-width="2"/>
                            <text x="340" y="345" text-anchor="middle" font-size="12" font-weight="bold" fill="white">配置文件</text>
                            <text x="340" y="360" text-anchor="middle" font-size="10" fill="white">Config</text>
                            
                            <!-- 事件系统 -->
                            <rect x="800" y="320" width="120" height="60" rx="10" fill="#f44336" stroke="#d32f2f" stroke-width="2"/>
                            <text x="860" y="345" text-anchor="middle" font-size="12" font-weight="bold" fill="white">发布订阅</text>
                            <text x="860" y="360" text-anchor="middle" font-size="10" fill="white">PubSub</text>
                            
                            <rect x="980" y="320" width="120" height="60" rx="10" fill="#f44336" stroke="#d32f2f" stroke-width="2"/>
                            <text x="1040" y="345" text-anchor="middle" font-size="12" font-weight="bold" fill="white">事件流</text>
                            <text x="1040" y="360" text-anchor="middle" font-size="10" fill="white">Events</text>
                            
                            <!-- 连接线定义 -->
                            <defs>
                                <marker id="componentArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                                </marker>
                                <marker id="bidirectionalArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#333"/>
                                </marker>
                            </defs>
                            
                            <!-- 应用服务到核心服务的连接 -->
                            <line x1="540" y1="300" x2="160" y2="180" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            <line x1="560" y1="290" x2="340" y2="180" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            <line x1="640" y1="300" x2="520" y2="180" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            <line x1="660" y1="300" x2="700" y2="180" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            <line x1="680" y1="320" x2="860" y2="180" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            <line x1="680" y1="340" x2="1040" y2="180" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            
                            <!-- 应用服务到AI系统的连接 -->
                            <line x1="540" y1="400" x2="160" y2="520" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            <line x1="560" y1="420" x2="340" y2="520" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            <line x1="600" y1="430" x2="520" y2="520" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            
                            <!-- 工具系统到外部集成的连接 -->
                            <line x1="580" y1="560" x2="800" y2="560" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            <line x1="580" y1="580" x2="980" y2="580" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            
                            <!-- 核心服务到数据存储的连接 -->
                            <line x1="160" y1="180" x2="160" y2="310" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            <line x1="340" y1="180" x2="340" y2="320" stroke="#666" stroke-width="2" marker-end="url(#componentArrow)"/>
                            
                            <!-- 事件系统连接 -->
                            <line x1="680" y1="350" x2="800" y2="350" stroke="#f44336" stroke-width="3" marker-end="url(#componentArrow)"/>
                            <line x1="920" y1="350" x2="980" y2="350" stroke="#f44336" stroke-width="3" marker-end="url(#componentArrow)"/>
                            
                            <!-- 事件流到各组件的连接 -->
                            <line x1="1040" y1="320" x2="1040" y2="180" stroke="#f44336" stroke-width="2" stroke-dasharray="5,5"/>
                            <line x1="1040" y1="320" x2="520" y2="180" stroke="#f44336" stroke-width="2" stroke-dasharray="5,5"/>
                            <line x1="1040" y1="320" x2="160" y2="180" stroke="#f44336" stroke-width="2" stroke-dasharray="5,5"/>
                            
                            <!-- 双向连接标识 -->
                            <text x="450" y="240" text-anchor="middle" font-size="10" fill="#666" transform="rotate(-45 450 240)">双向通信</text>
                            <text x="750" y="240" text-anchor="middle" font-size="10" fill="#666" transform="rotate(45 750 240)">双向通信</text>
                            
                            <!-- 连接标签 -->
                            <text x="250" y="250" text-anchor="middle" font-size="9" fill="#666">数据存储</text>
                            <text x="450" y="460" text-anchor="middle" font-size="9" fill="#666">AI处理</text>
                            <text x="750" y="540" text-anchor="middle" font-size="9" fill="#666">外部集成</text>
                            <text x="900" y="280" text-anchor="middle" font-size="9" fill="#f44336">事件传递</text>
                        </svg>
                    </div>
                    
                    <div class="legend">
                        <h4>组件类型说明</h4>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #ff9800;"></span>
                            核心应用服务 - 系统调度中心
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #4caf50;"></span>
                            业务服务 - 核心业务逻辑处理
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #9c27b0;"></span>
                            AI系统 - 智能处理和推理
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #2196f3;"></span>
                            用户界面 - 交互界面组件
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #00bcd4;"></span>
                            外部集成 - 第三方服务集成
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #607d8b;"></span>
                            数据存储 - 持久化存储
                        </div>
                        <div class="legend-item">
                            <span class="legend-color" style="background: #f44336;"></span>
                            事件系统 - 消息传递机制
                        </div>
                    </div>
                </div>
            </div>

            <!-- 工具系统架构图 -->
            <div id="tools" class="tab-content">
                <div class="diagram-section">
                    <h2 class="diagram-title">🛠️ 工具系统架构</h2>
                    <p class="diagram-description">
                        详细展示OpenCode工具系统的架构设计，包括权限控制、工具调用和执行环境的完整流程。
                    </p>
                    <div class="svg-container">
                        <svg viewBox="0 0 1200 800" xmlns="http://www.w3.org/2000/svg">
                            <!-- 工具接口层 -->
                            <rect x="50" y="50" width="1100" height="100" rx="10" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>
                            <text x="600" y="80" text-anchor="middle" font-size="18" font-weight="bold" fill="#1976d2">工具接口层</text>
                            
                            <rect x="100" y="90" width="150" height="40" rx="5" fill="#bbdefb" stroke="#2196f3"/>
                            <text x="175" y="115" text-anchor="middle" font-size="12" fill="#1976d2">工具接口</text>
                            
                            <rect x="300" y="90" width="150" height="40" rx="5" fill="#bbdefb" stroke="#2196f3"/>
                            <text x="375" y="115" text-anchor="middle" font-size="12" fill="#1976d2">工具调用</text>
                            
                            <rect x="500" y="90" width="150" height="40" rx="5" fill="#bbdefb" stroke="#2196f3"/>
                            <text x="575" y="115" text-anchor="middle" font-size="12" fill="#1976d2">工具响应</text>
                            
                            <rect x="700" y="90" width="150" height="40" rx="5" fill="#bbdefb" stroke="#2196f3"/>
                            <text x="775" y="115" text-anchor="middle" font-size="12" fill="#1976d2">错误处理</text>
                            
                            <!-- 权限控制层 -->
                            <rect x="50" y="180" width="1100" height="100" rx="10" fill="#ffebee" stroke="#f44336" stroke-width="2"/>
                            <text x="600" y="210" text-anchor="middle" font-size="18" font-weight="bold" fill="#d32f2f">权限控制层</text>
                            
                            <rect x="100" y="220" width="150" height="40" rx="5" fill="#ffcdd2" stroke="#f44336"/>
                            <text x="175" y="245" text-anchor="middle" font-size="12" fill="#d32f2f">权限检查</text>
                            
                            <rect x="300" y="220" width="150" height="40" rx="5" fill="#ffcdd2" stroke="#f44336"/>
                            <text x="375" y="245" text-anchor="middle" font-size="12" fill="#d32f2f">权限请求</text>
                            
                            <rect x="500" y="220" width="150" height="40" rx="5" fill="#ffcdd2" stroke="#f44336"/>
                            <text x="575" y="245" text-anchor="middle" font-size="12" fill="#d32f2f">权限对话框</text>
                            
                            <rect x="700" y="220" width="150" height="40" rx="5" fill="#ffcdd2" stroke="#f44336"/>
                            <text x="775" y="245" text-anchor="middle" font-size="12" fill="#d32f2f">权限缓存</text>
                            
                            <!-- 核心工具层 -->
                            <rect x="50" y="310" width="550" height="180" rx="10" fill="#e8f5e8" stroke="#4caf50" stroke-width="2"/>
                            <text x="325" y="340" text-anchor="middle" font-size="18" font-weight="bold" fill="#388e3c">核心工具</text>
                            
                            <rect x="80" y="360" width="100" height="50" rx="5" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="130" y="380" text-anchor="middle" font-size="11" fill="#2e7d32">Shell工具</text>
                            <text x="130" y="395" text-anchor="middle" font-size="9" fill="#2e7d32">BashTool</text>
                            
                            <rect x="200" y="360" width="100" height="50" rx="5" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="250" y="380" text-anchor="middle" font-size="11" fill="#2e7d32">编辑工具</text>
                            <text x="250" y="395" text-anchor="middle" font-size="9" fill="#2e7d32">EditTool</text>
                            
                            <rect x="320" y="360" width="100" height="50" rx="5" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="370" y="380" text-anchor="middle" font-size="11" fill="#2e7d32">文件工具</text>
                            <text x="370" y="395" text-anchor="middle" font-size="9" fill="#2e7d32">FileTool</text>
                            
                            <rect x="440" y="360" width="100" height="50" rx="5" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="490" y="380" text-anchor="middle" font-size="11" fill="#2e7d32">搜索工具</text>
                            <text x="490" y="395" text-anchor="middle" font-size="9" fill="#2e7d32">SearchTool</text>
                            
                            <rect x="80" y="420" width="100" height="50" rx="5" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="130" y="440" text-anchor="middle" font-size="11" fill="#2e7d32">LSP工具</text>
                            <text x="130" y="455" text-anchor="middle" font-size="9" fill="#2e7d32">LSPTool</text>
                            
                            <rect x="200" y="420" width="100" height="50" rx="5" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="250" y="440" text-anchor="middle" font-size="11" fill="#2e7d32">网络工具</text>
                            <text x="250" y="455" text-anchor="middle" font-size="9" fill="#2e7d32">WebTool</text>
                            
                            <rect x="320" y="420" width="100" height="50" rx="5" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="370" y="440" text-anchor="middle" font-size="11" fill="#2e7d32">诊断工具</text>
                            <text x="370" y="455" text-anchor="middle" font-size="9" fill="#2e7d32">DiagTool</text>
                            
                            <rect x="440" y="420" width="100" height="50" rx="5" fill="#c8e6c9" stroke="#4caf50"/>
                            <text x="490" y="440" text-anchor="middle" font-size="11" fill="#2e7d32">代理工具</text>
                            <text x="490" y="455" text-anchor="middle" font-size="9" fill="#2e7d32">AgentTool</text>
                            
                            <!-- 外部工具层 -->
                            <rect x="620" y="310" width="530" height="180" rx="10" fill="#e1f5fe" stroke="#00bcd4" stroke-width="2"/>
                            <text x="885" y="340" text-anchor="middle" font-size="18" font-weight="bold" fill="#0097a7">外部工具</text>
                            
                            <rect x="650" y="360" width="120" height="50" rx="5" fill="#b2ebf2" stroke="#00bcd4"/>
                            <text x="710" y="380" text-anchor="middle" font-size="11" fill="#006064">MCP工具</text>
                            <text x="710" y="395" text-anchor="middle" font-size="9" fill="#006064">MCPTool</text>
                            
                            <rect x="790" y="360" width="120" height="50" rx="5" fill="#b2ebf2" stroke="#00bcd4"/>
                            <text x="850" y="380" text-anchor="middle" font-size="11" fill="#006064">自定义工具</text>
                            <text x="850" y="395" text-anchor="middle" font-size="9" fill="#006064">CustomTool</text>
                            
                            <rect x="930" y="360" width="120" height="50" rx="5" fill="#b2ebf2" stroke="#00bcd4"/>
                            <text x="990" y="380" text-anchor="middle" font-size="11" fill="#006064">插件工具</text>
                            <text x="990" y="395" text-anchor="middle" font-size="9" fill="#006064">PluginTool</text>
                            
                            <rect x="720" y="420" width="120" height="50" rx="5" fill="#b2ebf2" stroke="#00bcd4"/>
                            <text x="780" y="440" text-anchor="middle" font-size="11" fill="#006064">扩展工具</text>
                            <text x="780" y="455" text-anchor="middle" font-size="9" fill="#006064">ExtTool</text>
                            
                            <rect x="860" y="420" width="120" height="50" rx="5" fill="#b2ebf2" stroke="#00bcd4"/>
                            <text x="920" y="440" text-anchor="middle" font-size="11" fill="#006064">第三方工具</text>
                            <text x="920" y="455" text-anchor="middle" font-size="9" fill="#006064">ThirdParty</text>
                            
                            <!-- 执行环境层 -->
                            <rect x="50" y="520" width="1100" height="120" rx="10" fill="#fff3e0" stroke="#ff9800" stroke-width="2"/>
                            <text x="600" y="550" text-anchor="middle" font-size="18" font-weight="bold" fill="#f57c00">执行环境</text>
                            
                            <rect x="100" y="570" width="120" height="50" rx="5" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="160" y="590" text-anchor="middle" font-size="11" fill="#e65100">文件系统</text>
                            <text x="160" y="605" text-anchor="middle" font-size="9" fill="#e65100">FileSystem</text>
                            
                            <rect x="240" y="570" width="120" height="50" rx="5" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="300" y="590" text-anchor="middle" font-size="11" fill="#e65100">Shell环境</text>
                            <text x="300" y="605" text-anchor="middle" font-size="9" fill="#e65100">Shell</text>
                            
                            <rect x="380" y="570" width="120" height="50" rx="5" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="440" y="590" text-anchor="middle" font-size="11" fill="#e65100">网络环境</text>
                            <text x="440" y="605" text-anchor="middle" font-size="9" fill="#e65100">Network</text>
                            
                            <rect x="520" y="570" width="120" height="50" rx="5" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="580" y="590" text-anchor="middle" font-size="11" fill="#e65100">LSP服务器</text>
                            <text x="580" y="605" text-anchor="middle" font-size="9" fill="#e65100">LSPServer</text>
                            
                            <rect x="660" y="570" width="120" height="50" rx="5" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="720" y="590" text-anchor="middle" font-size="11" fill="#e65100">MCP服务器</text>
                            <text x="720" y="605" text-anchor="middle" font-size="9" fill="#e65100">MCPServer</text>
                            
                            <rect x="800" y="570" width="120" height="50" rx="5" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="860" y="590" text-anchor="middle" font-size="11" fill="#e65100">容器环境</text>
                            <text x="860" y="605" text-anchor="middle" font-size="9" fill="#e65100">Container</text>
                            
                            <rect x="940" y="570" width="120" height="50" rx="5" fill="#ffe0b2" stroke="#ff9800"/>
                            <text x="1000" y="590" text-anchor="middle" font-size="11" fill="#e65100">云服务</text>
                            <text x="1000" y="605" text-anchor="middle" font-size="9" fill="#e65100">Cloud</text>
                            
                            <!-- 连接线 -->
                            <defs>
                                <marker id="toolArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                                </marker>
                            </defs>
                            
                            <!-- 接口层到权限层 -->
                            <line x1="175" y1="130" x2="175" y2="220" stroke="#666" stroke-width="2" marker-end="url(#toolArrow)"/>
                            <line x1="375" y1="130" x2="375" y2="220" stroke="#666" stroke-width="2" marker-end="url(#toolArrow)"/>
                            <line x1="575" y1="130" x2="575" y2="220" stroke="#666" stroke-width="2" marker-end="url(#toolArrow)"/>
                            
                            <!-- 权限层到工具层 -->
                            <line x1="175" y1="260" x2="130" y2="360" stroke="#666" stroke-width="2" marker-end="url(#toolArrow)"/>
                            <line x1="375" y1="260" x2="370" y2="360" stroke="#666" stroke-width="2" marker-end="url(#toolArrow)"/>
                            <line x1="575" y1="260" x2="710" y2="360" stroke="#666" stroke-width="2" marker-end="url(#toolArrow)"/>
                            
                            <!-- 工具层到执行环境 -->
                            <line x1="130" y1="470" x2="160" y2="570" stroke="#666" stroke-width="2" marker-end="url(#toolArrow)"/>
                            <line x1="250" y1="470" x2="300" y2="570" stroke="#666" stroke-width="2" marker-end="url(#toolArrow)"/>
                            <line x1="370" y1="470" x2="440" y2="570" stroke="#666" stroke-width="2" marker-end="url(#toolArrow)"/>
                            <line x1="490" y1="470" x2="580" y2="570" stroke="#666" stroke-width="2" marker-end="url(#toolArrow)"/>
                            <line x1="710" y1="470" x2="720" y2="570" stroke="#666" stroke-width="2" marker-end="url(#toolArrow)"/>
                            
                            <!-- 权限流程箭头 -->
                            <path d="M 250 240 Q 350 200 450 240" stroke="#f44336" stroke-width="3" fill="none" marker-end="url(#toolArrow)"/>
                            <text x="350" y="210" text-anchor="middle" font-size="10" fill="#f44336">权限流程</text>
                            
                            <!-- 执行流程箭头 -->
                            <path d="M 400 400 Q 600 450 800 400" stroke="#4caf50" stroke-width="3" fill="none" marker-end="url(#toolArrow)"/>
                            <text x="600" y="440" text-anchor="middle" font-size="10" fill="#4caf50">执行流程</text>
                            
                            <!-- 数据流标识 -->
                            <rect x="50" y="680" width="200" height="80" rx="10" fill="#f5f5f5" stroke="#999" stroke-width="1"/>
                            <text x="150" y="700" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">数据流向</text>
                            <line x1="80" y1="710" x2="120" y2="710" stroke="#666" stroke-width="2" marker-end="url(#toolArrow)"/>
                            <text x="140" y="715" font-size="10" fill="#666">单向调用</text>
                            <line x1="80" y1="730" x2="120" y2="730" stroke="#f44336" stroke-width="3" marker-end="url(#toolArrow)"/>
                            <text x="140" y="735" font-size="10" fill="#f44336">权限控制</text>
                            <line x1="80" y1="750" x2="120" y2="750" stroke="#4caf50" stroke-width="3" marker-end="url(#toolArrow)"/>
                            <text x="140" y="755" font-size="10" fill="#4caf50">执行流程</text>
                        </svg>
                    </div>
                    
                    <div class="legend">
                        <h4>工具系统特性</h4>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px; font-size: 0.9em;">
                            <div><strong>权限控制:</strong> 所有工具调用都需要通过权限检查</div>
                            <div><strong>安全执行:</strong> 危险操作需要用户明确确认</div>
                            <div><strong>模块化设计:</strong> 工具可以独立开发和部署</div>
                            <div><strong>扩展性:</strong> 支持MCP协议和自定义工具</div>
                            <div><strong>环境隔离:</strong> 不同工具在独立环境中执行</div>
                            <div><strong>错误处理:</strong> 完善的错误捕获和恢复机制</div>
                        </div>
                    </div>
                </div>
            </div>  
          <!-- 执行序列图 -->
            <div id="sequence" class="tab-content">
                <div class="diagram-section">
                    <h2 class="diagram-title">⚡ 任务执行序列图</h2>
                    <p class="diagram-description">
                        展示OpenCode处理用户任务的完整时序流程，从用户输入到最终响应的详细执行步骤。
                    </p>
                    <div class="svg-container">
                        <svg viewBox="0 0 1400 900" xmlns="http://www.w3.org/2000/svg">
                            <!-- 参与者列 -->
                            <rect x="50" y="50" width="100" height="40" rx="5" fill="#e3f2fd" stroke="#2196f3" stroke-width="2"/>
                            <text x="100" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="#1976d2">用户</text>
                            
                            <rect x="200" y="50" width="100" height="40" rx="5" fill="#f3e5f5" stroke="#9c27b0" stroke-width="2"/>
                            <text x="250" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="#7b1fa2">TUI</text>
                            
                            <rect x="350" y="50" width="100" height="40" rx="5" fill="#e8f5e8" stroke="#4caf50" stroke-width="2"/>
                            <text x="400" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="#388e3c">App</text>
                            
                            <rect x="500" y="50" width="100" height="40" rx="5" fill="#fff3e0" stroke="#ff9800" stroke-width="2"/>
                            <text x="550" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="#f57c00">Agent</text>
                            
                            <rect x="650" y="50" width="100" height="40" rx="5" fill="#ffebee" stroke="#f44336" stroke-width="2"/>
                            <text x="700" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="#d32f2f">LLM</text>
                            
                            <rect x="800" y="50" width="100" height="40" rx="5" fill="#e1f5fe" stroke="#00bcd4" stroke-width="2"/>
                            <text x="850" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="#0097a7">Tools</text>
                            
                            <rect x="950" y="50" width="100" height="40" rx="5" fill="#fce4ec" stroke="#e91e63" stroke-width="2"/>
                            <text x="1000" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="#c2185b">Permission</text>
                            
                            <rect x="1100" y="50" width="100" height="40" rx="5" fill="#e8eaf6" stroke="#3f51b5" stroke-width="2"/>
                            <text x="1150" y="75" text-anchor="middle" font-size="12" font-weight="bold" fill="#303f9f">Database</text>
                            
                            <!-- 生命线 -->
                            <line x1="100" y1="90" x2="100" y2="850" stroke="#ddd" stroke-width="2" stroke-dasharray="5,5"/>
                            <line x1="250" y1="90" x2="250" y2="850" stroke="#ddd" stroke-width="2" stroke-dasharray="5,5"/>
                            <line x1="400" y1="90" x2="400" y2="850" stroke="#ddd" stroke-width="2" stroke-dasharray="5,5"/>
                            <line x1="550" y1="90" x2="550" y2="850" stroke="#ddd" stroke-width="2" stroke-dasharray="5,5"/>
                            <line x1="700" y1="90" x2="700" y2="850" stroke="#ddd" stroke-width="2" stroke-dasharray="5,5"/>
                            <line x1="850" y1="90" x2="850" y2="850" stroke="#ddd" stroke-width="2" stroke-dasharray="5,5"/>
                            <line x1="1000" y1="90" x2="1000" y2="850" stroke="#ddd" stroke-width="2" stroke-dasharray="5,5"/>
                            <line x1="1150" y1="90" x2="1150" y2="850" stroke="#ddd" stroke-width="2" stroke-dasharray="5,5"/>
                            
                            <!-- 消息箭头定义 -->
                            <defs>
                                <marker id="seqArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#333"/>
                                </marker>
                                <marker id="returnArrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                                    <polygon points="0 0, 8 3, 0 6" fill="#666"/>
                                </marker>
                            </defs>
                            
                            <!-- 序列消息 -->
                            <!-- 1. 用户输入 -->
                            <line x1="100" y1="120" x2="250" y2="120" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="175" y="115" text-anchor="middle" font-size="10" fill="#333">1. 输入任务</text>
                            
                            <!-- 2. TUI处理 -->
                            <line x1="250" y1="140" x2="400" y2="140" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="325" y="135" text-anchor="middle" font-size="10" fill="#333">2. 处理输入</text>
                            
                            <!-- 3. 创建会话 -->
                            <line x1="400" y1="160" x2="1150" y2="160" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="775" y="155" text-anchor="middle" font-size="10" fill="#333">3. 创建/获取会话</text>
                            
                            <line x1="1150" y1="180" x2="400" y2="180" stroke="#666" stroke-width="2" marker-end="url(#returnArrow)" stroke-dasharray="3,3"/>
                            <text x="775" y="175" text-anchor="middle" font-size="10" fill="#666">会话ID</text>
                            
                            <!-- 4. 启动Agent -->
                            <line x1="400" y1="200" x2="550" y2="200" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="475" y="195" text-anchor="middle" font-size="10" fill="#333">4. 启动Agent</text>
                            
                            <!-- 5. 保存用户消息 -->
                            <line x1="550" y1="220" x2="1150" y2="220" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="850" y="215" text-anchor="middle" font-size="10" fill="#333">5. 保存用户消息</text>
                            
                            <!-- 6. 构建上下文 -->
                            <rect x="530" y="240" width="40" height="60" fill="#fff3e0" stroke="#ff9800"/>
                            <text x="550" y="275" text-anchor="middle" font-size="9" fill="#f57c00">构建</text>
                            <text x="550" y="285" text-anchor="middle" font-size="9" fill="#f57c00">上下文</text>
                            
                            <!-- 7. LLM推理循环开始 -->
                            <rect x="500" y="320" width="450" height="300" rx="10" fill="#f5f5f5" stroke="#999" stroke-width="1" stroke-dasharray="5,5"/>
                            <text x="725" y="340" text-anchor="middle" font-size="12" font-weight="bold" fill="#666">推理-执行循环</text>
                            
                            <!-- 7.1 发送到LLM -->
                            <line x1="550" y1="360" x2="700" y2="360" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="625" y="355" text-anchor="middle" font-size="10" fill="#333">7. 发送上下文</text>
                            
                            <!-- 7.2 流式响应 -->
                            <line x1="700" y1="380" x2="550" y2="380" stroke="#666" stroke-width="2" marker-end="url(#returnArrow)" stroke-dasharray="3,3"/>
                            <text x="625" y="375" text-anchor="middle" font-size="10" fill="#666">流式响应</text>
                            
                            <!-- 7.3 工具调用判断 -->
                            <polygon points="530,400 570,400 550,420" fill="#ffeb3b" stroke="#f57c00"/>
                            <text x="550" y="430" text-anchor="middle" font-size="9" fill="#f57c00">工具调用?</text>
                            
                            <!-- 7.4 工具执行分支 -->
                            <line x1="570" y1="410" x2="850" y2="410" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="710" y="405" text-anchor="middle" font-size="10" fill="#333">8. 调用工具</text>
                            
                            <!-- 7.5 权限检查 -->
                            <line x1="850" y1="430" x2="1000" y2="430" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="925" y="425" text-anchor="middle" font-size="10" fill="#333">9. 权限检查</text>
                            
                            <!-- 7.6 权限确认 -->
                            <line x1="1000" y1="450" x2="250" y2="450" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="625" y="445" text-anchor="middle" font-size="10" fill="#333">10. 权限确认</text>
                            
                            <line x1="250" y1="470" x2="100" y2="470" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="175" y="465" text-anchor="middle" font-size="10" fill="#333">显示权限请求</text>
                            
                            <line x1="100" y1="490" x2="250" y2="490" stroke="#666" stroke-width="2" marker-end="url(#returnArrow)" stroke-dasharray="3,3"/>
                            <text x="175" y="485" text-anchor="middle" font-size="10" fill="#666">用户确认</text>
                            
                            <line x1="250" y1="510" x2="1000" y2="510" stroke="#666" stroke-width="2" marker-end="url(#returnArrow)" stroke-dasharray="3,3"/>
                            <text x="625" y="505" text-anchor="middle" font-size="10" fill="#666">权限结果</text>
                            
                            <!-- 7.7 执行工具 -->
                            <line x1="1000" y1="530" x2="850" y2="530" stroke="#666" stroke-width="2" marker-end="url(#returnArrow)" stroke-dasharray="3,3"/>
                            <text x="925" y="525" text-anchor="middle" font-size="10" fill="#666">11. 权限通过</text>
                            
                            <rect x="830" y="540" width="40" height="40" fill="#e1f5fe" stroke="#00bcd4"/>
                            <text x="850" y="565" text-anchor="middle" font-size="9" fill="#0097a7">执行</text>
                            
                            <!-- 7.8 返回结果 -->
                            <line x1="850" y1="590" x2="550" y2="590" stroke="#666" stroke-width="2" marker-end="url(#returnArrow)" stroke-dasharray="3,3"/>
                            <text x="700" y="585" text-anchor="middle" font-size="10" fill="#666">12. 工具结果</text>
                            
                            <!-- 循环结束判断 -->
                            <polygon points="530,610 570,610 550,630" fill="#4caf50" stroke="#388e3c"/>
                            <text x="550" y="640" text-anchor="middle" font-size="9" fill="#388e3c">完成?</text>
                            
                            <!-- 8. 保存响应 -->
                            <line x1="550" y1="660" x2="1150" y2="660" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="850" y="655" text-anchor="middle" font-size="10" fill="#333">13. 保存响应</text>
                            
                            <!-- 9. 更新界面 -->
                            <line x1="550" y1="680" x2="250" y2="680" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="400" y="675" text-anchor="middle" font-size="10" fill="#333">14. 更新界面</text>
                            
                            <!-- 10. 显示结果 -->
                            <line x1="250" y1="700" x2="100" y2="700" stroke="#333" stroke-width="2" marker-end="url(#seqArrow)"/>
                            <text x="175" y="695" text-anchor="middle" font-size="10" fill="#333">15. 显示结果</text>
                            
                            <!-- 激活框 -->
                            <rect x="95" y="120" width="10" height="580" fill="#e3f2fd" stroke="#2196f3"/>
                            <rect x="245" y="140" width="10" height="560" fill="#f3e5f5" stroke="#9c27b0"/>
                            <rect x="395" y="160" width="10" height="520" fill="#e8f5e8" stroke="#4caf50"/>
                            <rect x="545" y="200" width="10" height="480" fill="#fff3e0" stroke="#ff9800"/>
                            <rect x="695" y="360" width="10" height="40" fill="#ffebee" stroke="#f44336"/>
                            <rect x="845" y="410" width="10" height="170" fill="#e1f5fe" stroke="#00bcd4"/>
                            <rect x="995" y="430" width="10" height="100" fill="#fce4ec" stroke="#e91e63"/>
                            <rect x="1145" y="160" width="10" height="500" fill="#e8eaf6" stroke="#3f51b5"/>
                            
                            <!-- 注释框 -->
                            <rect x="50" y="750" width="300" height="80" rx="5" fill="#f9f9f9" stroke="#ccc"/>
                            <text x="200" y="770" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">关键特性</text>
                            <text x="60" y="790" font-size="10" fill="#666">• 异步流式处理</text>
                            <text x="60" y="805" font-size="10" fill="#666">• 权限控制机制</text>
                            <text x="60" y="820" font-size="10" fill="#666">• 循环推理执行</text>
                            
                            <rect x="370" y="750" width="300" height="80" rx="5" fill="#f9f9f9" stroke="#ccc"/>
                            <text x="520" y="770" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">错误处理</text>
                            <text x="380" y="790" font-size="10" fill="#666">• 权限拒绝处理</text>
                            <text x="380" y="805" font-size="10" fill="#666">• 工具执行失败恢复</text>
                            <text x="380" y="820" font-size="10" fill="#666">• 用户取消操作</text>
                            
                            <rect x="690" y="750" width="300" height="80" rx="5" fill="#f9f9f9" stroke="#ccc"/>
                            <text x="840" y="770" text-anchor="middle" font-size="12" font-weight="bold" fill="#333">性能优化</text>
                            <text x="700" y="790" font-size="10" fill="#666">• 并发处理</text>
                            <text x="700" y="805" font-size="10" fill="#666">• 上下文缓存</text>
                            <text x="700" y="820" font-size="10" fill="#666">• 会话管理</text>
                        </svg>
                    </div>
                    
                    <div class="legend">
                        <h4>序列图说明</h4>
                        <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; font-size: 0.9em;">
                            <div>
                                <strong>实线箭头:</strong> 同步调用<br>
                                <strong>虚线箭头:</strong> 异步返回<br>
                                <strong>激活框:</strong> 组件活跃期间
                            </div>
                            <div>
                                <strong>循环框:</strong> 推理-执行循环<br>
                                <strong>判断框:</strong> 条件分支<br>
                                <strong>执行框:</strong> 处理过程
                            </div>
                            <div>
                                <strong>权限流:</strong> 安全控制流程<br>
                                <strong>数据流:</strong> 信息传递<br>
                                <strong>用户交互:</strong> 界面操作
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的活跃状态
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            
            // 激活选中的标签
            event.target.classList.add('active');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 为所有SVG添加缩放功能
            const svgs = document.querySelectorAll('svg');
            svgs.forEach(svg => {
                svg.style.cursor = 'grab';
                
                let isMouseDown = false;
                let startX, startY, scrollLeft, scrollTop;
                
                svg.addEventListener('mousedown', (e) => {
                    isMouseDown = true;
                    svg.style.cursor = 'grabbing';
                    startX = e.pageX - svg.offsetLeft;
                    startY = e.pageY - svg.offsetTop;
                    scrollLeft = svg.parentElement.scrollLeft;
                    scrollTop = svg.parentElement.scrollTop;
                });
                
                svg.addEventListener('mouseleave', () => {
                    isMouseDown = false;
                    svg.style.cursor = 'grab';
                });
                
                svg.addEventListener('mouseup', () => {
                    isMouseDown = false;
                    svg.style.cursor = 'grab';
                });
                
                svg.addEventListener('mousemove', (e) => {
                    if (!isMouseDown) return;
                    e.preventDefault();
                    const x = e.pageX - svg.offsetLeft;
                    const y = e.pageY - svg.offsetTop;
                    const walkX = (x - startX) * 2;
                    const walkY = (y - startY) * 2;
                    svg.parentElement.scrollLeft = scrollLeft - walkX;
                    svg.parentElement.scrollTop = scrollTop - walkY;
                });
            });
            
            // 添加键盘导航
            document.addEventListener('keydown', function(e) {
                if (e.key >= '1' && e.key <= '5') {
                    const tabNames = ['overview', 'dataflow', 'components', 'tools', 'sequence'];
                    const index = parseInt(e.key) - 1;
                    if (index < tabNames.length) {
                        showTab(tabNames[index]);
                        document.querySelectorAll('.nav-tab')[index].classList.add('active');
                    }
                }
            });
        });
    </script>
</body>
</html>