<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenCode Tools 手册</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .header {
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .toc {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .toc h2 {
            margin-top: 0;
            color: #667eea;
        }
        
        .toc ul {
            list-style: none;
            padding: 0;
        }
        
        .toc li {
            margin: 8px 0;
        }
        
        .toc a {
            color: #333;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            transition: background 0.2s;
        }
        
        .toc a:hover {
            background: #f0f0f0;
        }
        
        .section {
            background: white;
            margin: 20px 0;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .tool-card {
            background: white;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            margin: 20px 0;
            overflow: hidden;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .tool-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        
        .tool-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
        }
        
        .tool-name {
            font-size: 1.5em;
            font-weight: 600;
            margin: 0;
        }
        
        .tool-purpose {
            margin: 5px 0 0 0;
            opacity: 0.9;
        }
        
        .tool-content {
            padding: 20px;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }
        
        .feature-item h4 {
            margin: 0 0 10px 0;
            color: #667eea;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .params-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        
        .params-table th,
        .params-table td {
            border: 1px solid #e1e5e9;
            padding: 12px;
            text-align: left;
        }
        
        .params-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .required {
            color: #e53e3e;
            font-weight: 600;
        }
        
        .optional {
            color: #38a169;
        }
        
        .warning {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .warning h4 {
            color: #e53e3e;
            margin: 0 0 10px 0;
        }
        
        .tip {
            background: #f0fff4;
            border: 1px solid #c6f6d5;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .tip h4 {
            color: #38a169;
            margin: 0 0 10px 0;
        }
        
        .svg-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
        }
        
        h2 {
            color: #667eea;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }
        
        h3 {
            color: #4a5568;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>OpenCode Tools 手册</h1>
        <p>AI 代码助手工具集完整指南</p>
    </div>

    <div class="toc">
        <h2>目录</h2>
        <ul>
            <li><a href="#overview">系统概览</a></li>
            <li><a href="#architecture">工具架构</a></li>
            <li><a href="#file-tools">文件操作工具</a></li>
            <li><a href="#search-tools">搜索工具</a></li>
            <li><a href="#execution-tools">执行工具</a></li>
            <li><a href="#utility-tools">实用工具</a></li>
            <li><a href="#best-practices">最佳实践</a></li>
        </ul>
    </div>

    <div class="section" id="overview">
        <h2>系统概览</h2>
        <p>OpenCode Tools 是一个为 AI 代码助手设计的工具集，提供了文件操作、代码搜索、命令执行等核心功能。每个工具都遵循统一的接口设计，支持权限控制和历史记录。</p>
        
        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <!-- Background -->
                <rect width="800" height="400" fill="#f8f9fa" stroke="#e1e5e9"/>
                
                <!-- Title -->
                <text x="400" y="30" text-anchor="middle" font-size="20" font-weight="bold" fill="#667eea">OpenCode Tools 生态系统</text>
                
                <!-- Core Interface -->
                <rect x="300" y="60" width="200" height="60" fill="#667eea" rx="10"/>
                <text x="400" y="85" text-anchor="middle" fill="white" font-weight="bold">BaseTool Interface</text>
                <text x="400" y="105" text-anchor="middle" fill="white" font-size="12">Info() | Run()</text>
                
                <!-- Tool Categories -->
                <!-- File Tools -->
                <rect x="50" y="160" width="150" height="180" fill="#e6f3ff" stroke="#667eea" rx="8"/>
                <text x="125" y="180" text-anchor="middle" font-weight="bold" fill="#667eea">文件操作</text>
                <text x="125" y="200" text-anchor="middle" font-size="12">View</text>
                <text x="125" y="220" text-anchor="middle" font-size="12">Edit</text>
                <text x="125" y="240" text-anchor="middle" font-size="12">Write</text>
                <text x="125" y="260" text-anchor="middle" font-size="12">Patch</text>
                <text x="125" y="280" text-anchor="middle" font-size="12">Diagnostics</text>
                
                <!-- Search Tools -->
                <rect x="220" y="160" width="150" height="180" fill="#f0fff4" stroke="#38a169" rx="8"/>
                <text x="295" y="180" text-anchor="middle" font-weight="bold" fill="#38a169">搜索工具</text>
                <text x="295" y="200" text-anchor="middle" font-size="12">Grep</text>
                <text x="295" y="220" text-anchor="middle" font-size="12">Glob</text>
                <text x="295" y="240" text-anchor="middle" font-size="12">LS</text>
                <text x="295" y="260" text-anchor="middle" font-size="12">Sourcegraph</text>
                
                <!-- Execution Tools -->
                <rect x="390" y="160" width="150" height="180" fill="#fff5f5" stroke="#e53e3e" rx="8"/>
                <text x="465" y="180" text-anchor="middle" font-weight="bold" fill="#e53e3e">执行工具</text>
                <text x="465" y="200" text-anchor="middle" font-size="12">Bash</text>
                <text x="465" y="220" text-anchor="middle" font-size="12">Shell</text>
                
                <!-- Utility Tools -->
                <rect x="560" y="160" width="150" height="180" fill="#fffbf0" stroke="#d69e2e" rx="8"/>
                <text x="635" y="180" text-anchor="middle" font-weight="bold" fill="#d69e2e">实用工具</text>
                <text x="635" y="200" text-anchor="middle" font-size="12">Fetch</text>
                
                <!-- Connections -->
                <line x1="400" y1="120" x2="125" y2="160" stroke="#667eea" stroke-width="2"/>
                <line x1="400" y1="120" x2="295" y2="160" stroke="#667eea" stroke-width="2"/>
                <line x1="400" y1="120" x2="465" y2="160" stroke="#667eea" stroke-width="2"/>
                <line x1="400" y1="120" x2="635" y2="160" stroke="#667eea" stroke-width="2"/>
                
                <!-- Support Services -->
                <rect x="250" y="360" width="100" height="30" fill="#f7fafc" stroke="#a0aec0" rx="4"/>
                <text x="300" y="378" text-anchor="middle" font-size="12">Permission</text>
                
                <rect x="370" y="360" width="100" height="30" fill="#f7fafc" stroke="#a0aec0" rx="4"/>
                <text x="420" y="378" text-anchor="middle" font-size="12">History</text>
                
                <rect x="490" y="360" width="100" height="30" fill="#f7fafc" stroke="#a0aec0" rx="4"/>
                <text x="540" y="378" text-anchor="middle" font-size="12">LSP</text>
            </svg>
        </div>
    </div>

    <div class="section" id="architecture">
        <h2>工具架构</h2>
        <p>所有工具都实现了 BaseTool 接口，提供统一的调用方式和响应格式。</p>
        
        <div class="code-block">
type BaseTool interface {
    Info() ToolInfo
    Run(ctx context.Context, params ToolCall) (ToolResponse, error)
}

type ToolInfo struct {
    Name        string
    Description string
    Parameters  map[string]any
    Required    []string
}

type ToolResponse struct {
    Type     toolResponseType
    Content  string
    Metadata string
    IsError  bool
}</div>

        <div class="svg-container">
            <svg width="700" height="300" viewBox="0 0 700 300">
                <!-- Tool Execution Flow -->
                <text x="350" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#667eea">工具执行流程</text>
                
                <!-- Steps -->
                <rect x="50" y="50" width="120" height="40" fill="#e6f3ff" stroke="#667eea" rx="6"/>
                <text x="110" y="75" text-anchor="middle" font-size="12">1. 参数解析</text>
                
                <rect x="200" y="50" width="120" height="40" fill="#f0fff4" stroke="#38a169" rx="6"/>
                <text x="260" y="75" text-anchor="middle" font-size="12">2. 权限检查</text>
                
                <rect x="350" y="50" width="120" height="40" fill="#fff5f5" stroke="#e53e3e" rx="6"/>
                <text x="410" y="75" text-anchor="middle" font-size="12">3. 执行操作</text>
                
                <rect x="500" y="50" width="120" height="40" fill="#fffbf0" stroke="#d69e2e" rx="6"/>
                <text x="560" y="75" text-anchor="middle" font-size="12">4. 返回结果</text>
                
                <!-- Arrows -->
                <path d="M170 70 L190 70" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                <path d="M320 70 L340 70" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                <path d="M470 70 L490 70" stroke="#667eea" stroke-width="2" marker-end="url(#arrowhead)"/>
                
                <!-- Support Services -->
                <rect x="150" y="150" width="100" height="30" fill="#f7fafc" stroke="#a0aec0" rx="4"/>
                <text x="200" y="168" text-anchor="middle" font-size="12">Permission Service</text>
                
                <rect x="300" y="150" width="100" height="30" fill="#f7fafc" stroke="#a0aec0" rx="4"/>
                <text x="350" y="168" text-anchor="middle" font-size="12">History Service</text>
                
                <rect x="450" y="150" width="100" height="30" fill="#f7fafc" stroke="#a0aec0" rx="4"/>
                <text x="500" y="168" text-anchor="middle" font-size="12">LSP Clients</text>
                
                <!-- Service connections -->
                <line x1="260" y1="90" x2="200" y2="150" stroke="#a0aec0" stroke-dasharray="3,3"/>
                <line x1="410" y1="90" x2="350" y2="150" stroke="#a0aec0" stroke-dasharray="3,3"/>
                <line x1="410" y1="90" x2="500" y2="150" stroke="#a0aec0" stroke-dasharray="3,3"/>
                
                <!-- Error Handling -->
                <rect x="250" y="220" width="200" height="40" fill="#fff5f5" stroke="#e53e3e" rx="6"/>
                <text x="350" y="245" text-anchor="middle" font-size="12">错误处理 & 响应格式化</text>
                
                <!-- Arrow marker definition -->
                <defs>
                    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                    </marker>
                </defs>
            </svg>
        </div>
    </div>
</body>
</html>    <
div class="section" id="file-tools">
        <h2>文件操作工具</h2>
        <p>文件操作工具提供了完整的文件读写、编辑和诊断功能。</p>

        <div class="tool-card">
            <div class="tool-header">
                <h3 class="tool-name">View Tool</h3>
                <p class="tool-purpose">文件查看工具 - 读取和显示文件内容</p>
            </div>
            <div class="tool-content">
                <h4>功能特性</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>行号显示</h4>
                        <p>自动为文件内容添加行号，便于定位和引用</p>
                    </div>
                    <div class="feature-item">
                        <h4>分页读取</h4>
                        <p>支持偏移量和限制参数，处理大文件</p>
                    </div>
                    <div class="feature-item">
                        <h4>文件类型检测</h4>
                        <p>自动识别图片文件并提供相应提示</p>
                    </div>
                    <div class="feature-item">
                        <h4>LSP 集成</h4>
                        <p>自动获取语言服务器诊断信息</p>
                    </div>
                </div>

                <h4>参数说明</h4>
                <table class="params-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>file_path</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>要读取的文件路径</td>
                    </tr>
                    <tr>
                        <td>offset</td>
                        <td>integer</td>
                        <td class="optional">否</td>
                        <td>起始行号（从0开始）</td>
                    </tr>
                    <tr>
                        <td>limit</td>
                        <td>integer</td>
                        <td class="optional">否</td>
                        <td>读取行数（默认2000）</td>
                    </tr>
                </table>

                <h4>使用示例</h4>
                <div class="code-block">
{
  "file_path": "/path/to/file.go",
  "offset": 0,
  "limit": 100
}</div>

                <div class="tip">
                    <h4>使用建议</h4>
                    <ul>
                        <li>在编辑文件前先使用 View 工具查看内容</li>
                        <li>对于大文件，使用 offset 参数分段查看</li>
                        <li>结合 Grep 工具先找到相关文件再查看</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="tool-card">
            <div class="tool-header">
                <h3 class="tool-name">Edit Tool</h3>
                <p class="tool-purpose">文件编辑工具 - 精确的文本替换和内容修改</p>
            </div>
            <div class="tool-content">
                <h4>功能特性</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>精确替换</h4>
                        <p>基于字符串匹配的精确内容替换</p>
                    </div>
                    <div class="feature-item">
                        <h4>创建文件</h4>
                        <p>支持创建新文件（old_string 为空）</p>
                    </div>
                    <div class="feature-item">
                        <h4>删除内容</h4>
                        <p>支持删除内容（new_string 为空）</p>
                    </div>
                    <div class="feature-item">
                        <h4>安全检查</h4>
                        <p>确保唯一匹配，防止意外修改</p>
                    </div>
                </div>

                <h4>参数说明</h4>
                <table class="params-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>file_path</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>要编辑的文件路径（必须是绝对路径）</td>
                    </tr>
                    <tr>
                        <td>old_string</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>要替换的文本（必须唯一匹配）</td>
                    </tr>
                    <tr>
                        <td>new_string</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>替换后的文本</td>
                    </tr>
                </table>

                <div class="warning">
                    <h4>重要注意事项</h4>
                    <ul>
                        <li><strong>唯一性要求：</strong>old_string 必须在文件中唯一匹配</li>
                        <li><strong>上下文要求：</strong>包含足够的上下文（3-5行）确保唯一性</li>
                        <li><strong>精确匹配：</strong>包括所有空白字符和缩进</li>
                        <li><strong>预读要求：</strong>编辑前必须先用 View 工具读取文件</li>
                    </ul>
                </div>

                <h4>使用示例</h4>
                <div class="code-block">
// 替换函数内容
{
  "file_path": "/path/to/file.go",
  "old_string": "func example() {\n    // old implementation\n    return nil\n}",
  "new_string": "func example() {\n    // new implementation\n    return result\n}"
}

// 创建新文件
{
  "file_path": "/path/to/new_file.go",
  "old_string": "",
  "new_string": "package main\n\nfunc main() {\n    fmt.Println(\"Hello\")\n}"
}</div>
            </div>
        </div>

        <div class="tool-card">
            <div class="tool-header">
                <h3 class="tool-name">Write Tool</h3>
                <p class="tool-purpose">文件写入工具 - 创建或完全重写文件</p>
            </div>
            <div class="tool-content">
                <h4>功能特性</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>完整重写</h4>
                        <p>完全替换文件内容或创建新文件</p>
                    </div>
                    <div class="feature-item">
                        <h4>目录创建</h4>
                        <p>自动创建必要的父目录</p>
                    </div>
                    <div class="feature-item">
                        <h4>冲突检测</h4>
                        <p>检测文件是否在读取后被修改</p>
                    </div>
                    <div class="feature-item">
                        <h4>差异生成</h4>
                        <p>自动生成修改前后的差异对比</p>
                    </div>
                </div>

                <h4>参数说明</h4>
                <table class="params-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>file_path</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>要写入的文件路径</td>
                    </tr>
                    <tr>
                        <td>content</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>要写入的完整内容</td>
                    </tr>
                </table>

                <div class="tip">
                    <h4>适用场景</h4>
                    <ul>
                        <li>创建全新的文件</li>
                        <li>完全重写现有文件</li>
                        <li>生成配置文件或模板</li>
                        <li>大幅度修改时比 Edit 工具更适合</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="tool-card">
            <div class="tool-header">
                <h3 class="tool-name">Patch Tool</h3>
                <p class="tool-purpose">补丁工具 - 应用 Git 风格的补丁文件</p>
            </div>
            <div class="tool-content">
                <h4>功能特性</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>Git 补丁</h4>
                        <p>支持标准 Git diff 格式的补丁</p>
                    </div>
                    <div class="feature-item">
                        <h4>多文件支持</h4>
                        <p>一次性应用多个文件的修改</p>
                    </div>
                    <div class="feature-item">
                        <h4>权限控制</h4>
                        <p>每个文件修改都需要权限确认</p>
                    </div>
                    <div class="feature-item">
                        <h4>统计信息</h4>
                        <p>提供添加和删除行数的统计</p>
                    </div>
                </div>

                <h4>参数说明</h4>
                <table class="params-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>patch_text</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>Git diff 格式的补丁内容</td>
                    </tr>
                </table>
            </div>
        </div>

        <div class="tool-card">
            <div class="tool-header">
                <h3 class="tool-name">Diagnostics Tool</h3>
                <p class="tool-purpose">诊断工具 - 获取语言服务器的诊断信息</p>
            </div>
            <div class="tool-content">
                <h4>功能特性</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>LSP 集成</h4>
                        <p>与语言服务器协议集成</p>
                    </div>
                    <div class="feature-item">
                        <h4>实时诊断</h4>
                        <p>获取语法错误、警告等信息</p>
                    </div>
                    <div class="feature-item">
                        <h4>多语言支持</h4>
                        <p>支持各种编程语言的诊断</p>
                    </div>
                    <div class="feature-item">
                        <h4>严重性分类</h4>
                        <p>按错误、警告、信息分类显示</p>
                    </div>
                </div>

                <h4>参数说明</h4>
                <table class="params-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>file_path</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>要诊断的文件路径</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="section" id="search-tools">
        <h2>搜索工具</h2>
        <p>搜索工具提供了强大的文件查找和内容搜索功能。</p>

        <div class="svg-container">
            <svg width="600" height="250" viewBox="0 0 600 250">
                <text x="300" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#38a169">搜索工具关系图</text>
                
                <!-- LS Tool -->
                <rect x="50" y="60" width="100" height="60" fill="#f0fff4" stroke="#38a169" rx="8"/>
                <text x="100" y="85" text-anchor="middle" font-weight="bold" fill="#38a169">LS</text>
                <text x="100" y="105" text-anchor="middle" font-size="12">目录结构</text>
                
                <!-- Glob Tool -->
                <rect x="200" y="60" width="100" height="60" fill="#f0fff4" stroke="#38a169" rx="8"/>
                <text x="250" y="85" text-anchor="middle" font-weight="bold" fill="#38a169">Glob</text>
                <text x="250" y="105" text-anchor="middle" font-size="12">文件名匹配</text>
                
                <!-- Grep Tool -->
                <rect x="350" y="60" width="100" height="60" fill="#f0fff4" stroke="#38a169" rx="8"/>
                <text x="400" y="85" text-anchor="middle" font-weight="bold" fill="#38a169">Grep</text>
                <text x="400" y="105" text-anchor="middle" font-size="12">内容搜索</text>
                
                <!-- Sourcegraph Tool -->
                <rect x="500" y="60" width="100" height="60" fill="#f0fff4" stroke="#38a169" rx="8"/>
                <text x="550" y="85" text-anchor="middle" font-weight="bold" fill="#38a169">Sourcegraph</text>
                <text x="550" y="105" text-anchor="middle" font-size="12">代码搜索</text>
                
                <!-- Search Flow -->
                <text x="300" y="160" text-anchor="middle" font-size="14" font-weight="bold" fill="#4a5568">典型搜索流程</text>
                
                <!-- Flow arrows -->
                <path d="M150 90 L190 90" stroke="#38a169" stroke-width="2" marker-end="url(#greenArrow)"/>
                <path d="M300 90 L340 90" stroke="#38a169" stroke-width="2" marker-end="url(#greenArrow)"/>
                
                <!-- Flow labels -->
                <text x="170" y="85" text-anchor="middle" font-size="10" fill="#38a169">探索</text>
                <text x="320" y="85" text-anchor="middle" font-size="10" fill="#38a169">定位</text>
                
                <!-- Use cases -->
                <text x="100" y="190" text-anchor="middle" font-size="11" fill="#4a5568">浏览项目</text>
                <text x="250" y="190" text-anchor="middle" font-size="11" fill="#4a5568">按名称查找</text>
                <text x="400" y="190" text-anchor="middle" font-size="11" fill="#4a5568">按内容查找</text>
                <text x="550" y="190" text-anchor="middle" font-size="11" fill="#4a5568">全局搜索</text>
                
                <defs>
                    <marker id="greenArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#38a169"/>
                    </marker>
                </defs>
            </svg>
        </div>

        <div class="tool-card">
            <div class="tool-header">
                <h3 class="tool-name">LS Tool</h3>
                <p class="tool-purpose">目录列表工具 - 显示目录结构</p>
            </div>
            <div class="tool-content">
                <h4>功能特性</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>树形结构</h4>
                        <p>以层次化树形结构显示目录内容</p>
                    </div>
                    <div class="feature-item">
                        <h4>智能过滤</h4>
                        <p>自动跳过隐藏文件和常见的构建目录</p>
                    </div>
                    <div class="feature-item">
                        <h4>自定义忽略</h4>
                        <p>支持自定义忽略模式</p>
                    </div>
                    <div class="feature-item">
                        <h4>限制保护</h4>
                        <p>限制最大文件数防止输出过多</p>
                    </div>
                </div>

                <h4>参数说明</h4>
                <table class="params-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>path</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>要列出的目录路径</td>
                    </tr>
                    <tr>
                        <td>ignore</td>
                        <td>array</td>
                        <td class="optional">否</td>
                        <td>要忽略的 glob 模式列表</td>
                    </tr>
                </table>

                <h4>使用示例</h4>
                <div class="code-block">
{
  "path": "/project/src",
  "ignore": ["*.test.js", "temp*"]
}</div>
            </div>
        </div>

        <div class="tool-card">
            <div class="tool-header">
                <h3 class="tool-name">Glob Tool</h3>
                <p class="tool-purpose">文件匹配工具 - 按模式查找文件</p>
            </div>
            <div class="tool-content">
                <h4>功能特性</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>模式匹配</h4>
                        <p>支持标准 glob 模式语法</p>
                    </div>
                    <div class="feature-item">
                        <h4>递归搜索</h4>
                        <p>支持 ** 进行递归目录搜索</p>
                    </div>
                    <div class="feature-item">
                        <h4>时间排序</h4>
                        <p>按修改时间排序，最新文件优先</p>
                    </div>
                    <div class="feature-item">
                        <h4>高性能</h4>
                        <p>优先使用 ripgrep 提升搜索性能</p>
                    </div>
                </div>

                <h4>Glob 模式语法</h4>
                <table class="params-table">
                    <tr>
                        <th>模式</th>
                        <th>说明</th>
                        <th>示例</th>
                    </tr>
                    <tr>
                        <td>*</td>
                        <td>匹配任意字符（不包括路径分隔符）</td>
                        <td>*.js</td>
                    </tr>
                    <tr>
                        <td>**</td>
                        <td>递归匹配任意路径</td>
                        <td>**/*.go</td>
                    </tr>
                    <tr>
                        <td>?</td>
                        <td>匹配单个字符</td>
                        <td>test?.js</td>
                    </tr>
                    <tr>
                        <td>[...]</td>
                        <td>匹配字符集合</td>
                        <td>*.[ch]</td>
                    </tr>
                    <tr>
                        <td>{...}</td>
                        <td>匹配多个选项</td>
                        <td>*.{js,ts}</td>
                    </tr>
                </table>

                <h4>使用示例</h4>
                <div class="code-block">
// 查找所有 Go 文件
{
  "pattern": "**/*.go",
  "path": "/project"
}

// 查找测试文件
{
  "pattern": "**/*_test.{go,js}",
  "path": "/project/src"
}</div>
            </div>
        </div>

        <div class="tool-card">
            <div class="tool-header">
                <h3 class="tool-name">Grep Tool</h3>
                <p class="tool-purpose">内容搜索工具 - 在文件中搜索文本模式</p>
            </div>
            <div class="tool-content">
                <h4>功能特性</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>正则表达式</h4>
                        <p>支持强大的正则表达式搜索</p>
                    </div>
                    <div class="feature-item">
                        <h4>字面量搜索</h4>
                        <p>支持字面量文本搜索模式</p>
                    </div>
                    <div class="feature-item">
                        <h4>文件过滤</h4>
                        <p>支持按文件类型过滤搜索范围</p>
                    </div>
                    <div class="feature-item">
                        <h4>上下文显示</h4>
                        <p>显示匹配行及其行号</p>
                    </div>
                </div>

                <h4>参数说明</h4>
                <table class="params-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>pattern</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>搜索的正则表达式模式</td>
                    </tr>
                    <tr>
                        <td>path</td>
                        <td>string</td>
                        <td class="optional">否</td>
                        <td>搜索的起始目录</td>
                    </tr>
                    <tr>
                        <td>include</td>
                        <td>string</td>
                        <td class="optional">否</td>
                        <td>包含的文件模式</td>
                    </tr>
                    <tr>
                        <td>literal_text</td>
                        <td>boolean</td>
                        <td class="optional">否</td>
                        <td>是否按字面量搜索</td>
                    </tr>
                </table>

                <h4>使用示例</h4>
                <div class="code-block">
// 搜索函数定义
{
  "pattern": "func\\s+\\w+",
  "include": "*.go",
  "literal_text": false
}

// 字面量搜索
{
  "pattern": "fmt.Println(",
  "include": "*.go",
  "literal_text": true
}</div>

                <div class="tip">
                    <h4>搜索技巧</h4>
                    <ul>
                        <li>使用 literal_text=true 搜索包含特殊字符的文本</li>
                        <li>结合 include 参数限制搜索范围提升性能</li>
                        <li>先用 Glob 找到相关文件再用 Grep 搜索内容</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="tool-card">
            <div class="tool-header">
                <h3 class="tool-name">Sourcegraph Tool</h3>
                <p class="tool-purpose">代码搜索工具 - 全局代码搜索服务</p>
            </div>
            <div class="tool-content">
                <h4>功能特性</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>全局搜索</h4>
                        <p>跨项目和仓库的代码搜索</p>
                    </div>
                    <div class="feature-item">
                        <h4>语义搜索</h4>
                        <p>理解代码语义的智能搜索</p>
                    </div>
                    <div class="feature-item">
                        <h4>上下文窗口</h4>
                        <p>可配置的搜索结果上下文</p>
                    </div>
                    <div class="feature-item">
                        <h4>结果限制</h4>
                        <p>可控制的搜索结果数量</p>
                    </div>
                </div>

                <h4>参数说明</h4>
                <table class="params-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>query</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>搜索查询字符串</td>
                    </tr>
                    <tr>
                        <td>count</td>
                        <td>integer</td>
                        <td class="optional">否</td>
                        <td>返回结果数量</td>
                    </tr>
                    <tr>
                        <td>context_window</td>
                        <td>integer</td>
                        <td class="optional">否</td>
                        <td>上下文窗口大小</td>
                    </tr>
                    <tr>
                        <td>timeout</td>
                        <td>integer</td>
                        <td class="optional">否</td>
                        <td>搜索超时时间</td>
                    </tr>
                </table>
            </div>
        </div>
    </div> 
   <div class="section" id="execution-tools">
        <h2>执行工具</h2>
        <p>执行工具提供了命令行执行和持久化 Shell 会话功能。</p>

        <div class="svg-container">
            <svg width="700" height="300" viewBox="0 0 700 300">
                <text x="350" y="25" text-anchor="middle" font-size="18" font-weight="bold" fill="#e53e3e">Bash 工具执行流程</text>
                
                <!-- Input -->
                <rect x="50" y="60" width="120" height="40" fill="#fff5f5" stroke="#e53e3e" rx="6"/>
                <text x="110" y="85" text-anchor="middle" font-size="12">命令输入</text>
                
                <!-- Security Check -->
                <rect x="200" y="60" width="120" height="40" fill="#fffbf0" stroke="#d69e2e" rx="6"/>
                <text x="260" y="85" text-anchor="middle" font-size="12">安全检查</text>
                
                <!-- Permission -->
                <rect x="350" y="60" width="120" height="40" fill="#f0fff4" stroke="#38a169" rx="6"/>
                <text x="410" y="85" text-anchor="middle" font-size="12">权限验证</text>
                
                <!-- Execution -->
                <rect x="500" y="60" width="120" height="40" fill="#e6f3ff" stroke="#667eea" rx="6"/>
                <text x="560" y="85" text-anchor="middle" font-size="12">命令执行</text>
                
                <!-- Arrows -->
                <path d="M170 80 L190 80" stroke="#e53e3e" stroke-width="2" marker-end="url(#redArrow)"/>
                <path d="M320 80 L340 80" stroke="#e53e3e" stroke-width="2" marker-end="url(#redArrow)"/>
                <path d="M470 80 L490 80" stroke="#e53e3e" stroke-width="2" marker-end="url(#redArrow)"/>
                
                <!-- Security Features -->
                <rect x="150" y="140" width="400" height="80" fill="#fff5f5" stroke="#e53e3e" rx="8"/>
                <text x="350" y="160" text-anchor="middle" font-weight="bold" fill="#e53e3e">安全特性</text>
                
                <text x="200" y="180" font-size="12" fill="#4a5568">• 禁止网络相关命令 (curl, wget, nc 等)</text>
                <text x="200" y="200" font-size="12" fill="#4a5568">• 安全只读命令白名单 (ls, echo, git status 等)</text>
                <text x="200" y="220" font-size="12" fill="#4a5568">• 输出长度限制和截断保护</text>
                
                <!-- Persistent Shell -->
                <rect x="50" y="240" width="600" height="40" fill="#f7fafc" stroke="#a0aec0" rx="6"/>
                <text x="350" y="265" text-anchor="middle" font-size="14" font-weight="bold" fill="#4a5568">持久化 Shell 会话 - 环境变量和工作目录保持</text>
                
                <defs>
                    <marker id="redArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#e53e3e"/>
                    </marker>
                </defs>
            </svg>
        </div>

        <div class="tool-card">
            <div class="tool-header">
                <h3 class="tool-name">Bash Tool</h3>
                <p class="tool-purpose">命令执行工具 - 在持久化 Shell 会话中执行命令</p>
            </div>
            <div class="tool-content">
                <h4>功能特性</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>持久化会话</h4>
                        <p>所有命令共享同一个 Shell 会话，环境变量和目录状态保持</p>
                    </div>
                    <div class="feature-item">
                        <h4>安全控制</h4>
                        <p>禁止危险命令，安全命令无需权限确认</p>
                    </div>
                    <div class="feature-item">
                        <h4>超时保护</h4>
                        <p>可配置的命令执行超时时间</p>
                    </div>
                    <div class="feature-item">
                        <h4>输出管理</h4>
                        <p>自动截断过长输出，防止响应过大</p>
                    </div>
                </div>

                <h4>参数说明</h4>
                <table class="params-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>command</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>要执行的 bash 命令</td>
                    </tr>
                    <tr>
                        <td>timeout</td>
                        <td>integer</td>
                        <td class="optional">否</td>
                        <td>超时时间（毫秒，最大600000）</td>
                    </tr>
                </table>

                <div class="warning">
                    <h4>禁止的命令</h4>
                    <p>以下命令被禁止使用以确保安全：</p>
                    <div class="code-block">alias, curl, curlie, wget, axel, aria2c, nc, telnet, lynx, w3m, links, httpie, xh, http-prompt, chrome, firefox, safari</div>
                </div>

                <div class="tip">
                    <h4>安全只读命令</h4>
                    <p>以下命令无需权限确认即可执行：</p>
                    <div class="code-block">ls, echo, pwd, date, whoami, git status, git log, git diff, go version, go help 等</div>
                </div>

                <h4>Git 提交流程</h4>
                <p>工具内置了完整的 Git 提交工作流程：</p>
                <ol>
                    <li>运行 <code>git status</code> 查看未跟踪文件</li>
                    <li>运行 <code>git diff</code> 查看变更</li>
                    <li>运行 <code>git log</code> 了解提交历史风格</li>
                    <li>分析变更并生成提交信息</li>
                    <li>执行提交并添加 AI 生成标识</li>
                </ol>

                <h4>使用示例</h4>
                <div class="code-block">
// 基本命令执行
{
  "command": "ls -la",
  "timeout": 5000
}

// Git 操作
{
  "command": "git status"
}

// 构建项目
{
  "command": "go build ./...",
  "timeout": 30000
}</div>

                <div class="tip">
                    <h4>最佳实践</h4>
                    <ul>
                        <li>避免使用 find、grep 等搜索命令，使用专门的搜索工具</li>
                        <li>避免使用 cat、head、tail 等读取命令，使用 View 工具</li>
                        <li>使用分号或 && 连接多个命令而不是换行</li>
                        <li>尽量使用绝对路径避免频繁 cd</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="tool-card">
            <div class="tool-header">
                <h3 class="tool-name">Persistent Shell</h3>
                <p class="tool-purpose">持久化 Shell - 底层 Shell 会话管理</p>
            </div>
            <div class="tool-content">
                <h4>功能特性</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>会话保持</h4>
                        <p>维护长期运行的 Shell 进程</p>
                    </div>
                    <div class="feature-item">
                        <h4>状态持久化</h4>
                        <p>环境变量、工作目录等状态保持</p>
                    </div>
                    <div class="feature-item">
                        <h4>并发安全</h4>
                        <p>支持并发命令执行的安全管理</p>
                    </div>
                    <div class="feature-item">
                        <h4>进程管理</h4>
                        <p>自动清理子进程，防止僵尸进程</p>
                    </div>
                </div>

                <h4>核心方法</h4>
                <table class="params-table">
                    <tr>
                        <th>方法</th>
                        <th>说明</th>
                        <th>返回值</th>
                    </tr>
                    <tr>
                        <td>Exec()</td>
                        <td>执行命令</td>
                        <td>stdout, stderr, exitCode, interrupted, error</td>
                    </tr>
                    <tr>
                        <td>Close()</td>
                        <td>关闭 Shell 会话</td>
                        <td>void</td>
                    </tr>
                    <tr>
                        <td>killChildren()</td>
                        <td>终止所有子进程</td>
                        <td>void</td>
                    </tr>
                </table>

                <div class="code-block">
type PersistentShell struct {
    cmd          *exec.Cmd
    stdin        *os.File
    isAlive      bool
    cwd          string
    mu           sync.Mutex
    commandQueue chan *commandExecution
}</div>
            </div>
        </div>
    </div>

    <div class="section" id="utility-tools">
        <h2>实用工具</h2>
        <p>实用工具提供了网络请求和其他辅助功能。</p>

        <div class="tool-card">
            <div class="tool-header">
                <h3 class="tool-name">Fetch Tool</h3>
                <p class="tool-purpose">网络请求工具 - 获取网络资源</p>
            </div>
            <div class="tool-content">
                <h4>功能特性</h4>
                <div class="feature-grid">
                    <div class="feature-item">
                        <h4>多格式支持</h4>
                        <p>支持 HTML、文本、Markdown 等格式</p>
                    </div>
                    <div class="feature-item">
                        <h4>内容转换</h4>
                        <p>自动将 HTML 转换为 Markdown</p>
                    </div>
                    <div class="feature-item">
                        <h4>权限控制</h4>
                        <p>网络请求需要权限确认</p>
                    </div>
                    <div class="feature-item">
                        <h4>超时保护</h4>
                        <p>可配置的请求超时时间</p>
                    </div>
                </div>

                <h4>参数说明</h4>
                <table class="params-table">
                    <tr>
                        <th>参数名</th>
                        <th>类型</th>
                        <th>必需</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>url</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>要请求的 URL</td>
                    </tr>
                    <tr>
                        <td>format</td>
                        <td>string</td>
                        <td class="required">是</td>
                        <td>响应格式 (html, text, markdown)</td>
                    </tr>
                    <tr>
                        <td>timeout</td>
                        <td>integer</td>
                        <td class="optional">否</td>
                        <td>超时时间（秒）</td>
                    </tr>
                </table>

                <h4>支持的格式</h4>
                <ul>
                    <li><strong>html</strong> - 返回原始 HTML 内容</li>
                    <li><strong>text</strong> - 提取纯文本内容</li>
                    <li><strong>markdown</strong> - 转换为 Markdown 格式</li>
                </ul>

                <h4>使用示例</h4>
                <div class="code-block">
// 获取网页并转换为 Markdown
{
  "url": "https://example.com/api/docs",
  "format": "markdown",
  "timeout": 30
}

// 获取纯文本内容
{
  "url": "https://api.example.com/status",
  "format": "text"
}</div>
            </div>
        </div>
    </div>

    <div class="section" id="best-practices">
        <h2>最佳实践</h2>

        <div class="svg-container">
            <svg width="800" height="400" viewBox="0 0 800 400">
                <text x="400" y="25" text-anchor="middle" font-size="20" font-weight="bold" fill="#667eea">工具使用最佳实践</text>
                
                <!-- Workflow Steps -->
                <rect x="50" y="60" width="140" height="60" fill="#e6f3ff" stroke="#667eea" rx="8"/>
                <text x="120" y="85" text-anchor="middle" font-weight="bold" fill="#667eea">1. 探索阶段</text>
                <text x="120" y="105" text-anchor="middle" font-size="12">LS → Glob → Grep</text>
                
                <rect x="220" y="60" width="140" height="60" fill="#f0fff4" stroke="#38a169" rx="8"/>
                <text x="290" y="85" text-anchor="middle" font-weight="bold" fill="#38a169">2. 理解阶段</text>
                <text x="290" y="105" text-anchor="middle" font-size="12">View → Diagnostics</text>
                
                <rect x="390" y="60" width="140" height="60" fill="#fff5f5" stroke="#e53e3e" rx="8"/>
                <text x="460" y="85" text-anchor="middle" font-weight="bold" fill="#e53e3e">3. 修改阶段</text>
                <text x="460" y="105" text-anchor="middle" font-size="12">Edit → Write → Patch</text>
                
                <rect x="560" y="60" width="140" height="60" fill="#fffbf0" stroke="#d69e2e" rx="8"/>
                <text x="630" y="85" text-anchor="middle" font-weight="bold" fill="#d69e2e">4. 验证阶段</text>
                <text x="630" y="105" text-anchor="middle" font-size="12">Bash → Diagnostics</text>
                
                <!-- Arrows -->
                <path d="M190 90 L210 90" stroke="#667eea" stroke-width="2" marker-end="url(#blueArrow)"/>
                <path d="M360 90 L380 90" stroke="#667eea" stroke-width="2" marker-end="url(#blueArrow)"/>
                <path d="M530 90 L550 90" stroke="#667eea" stroke-width="2" marker-end="url(#blueArrow)"/>
                
                <!-- Best Practices -->
                <rect x="50" y="160" width="700" height="200" fill="#f8f9fa" stroke="#e1e5e9" rx="8"/>
                <text x="400" y="185" text-anchor="middle" font-size="16" font-weight="bold" fill="#4a5568">核心原则</text>
                
                <!-- Principles -->
                <text x="80" y="210" font-size="14" font-weight="bold" fill="#667eea">🔍 先搜索后操作</text>
                <text x="80" y="230" font-size="12" fill="#4a5568">使用 LS、Glob、Grep 工具先了解项目结构和定位目标文件</text>
                
                <text x="80" y="255" font-size="14" font-weight="bold" fill="#38a169">📖 先读取后编辑</text>
                <text x="80" y="275" font-size="12" fill="#4a5568">使用 View 工具查看文件内容后再进行 Edit 或 Write 操作</text>
                
                <text x="80" y="300" font-size="14" font-weight="bold" fill="#e53e3e">⚡ 选择合适工具</text>
                <text x="80" y="320" font-size="12" fill="#4a5568">小修改用 Edit，大修改用 Write，多文件修改用 Patch</text>
                
                <text x="80" y="345" font-size="14" font-weight="bold" fill="#d69e2e">🛡️ 权限和安全</text>
                <text x="80" y="365" font-size="12" fill="#4a5568">理解权限系统，使用安全的命令，避免危险操作</text>
                
                <defs>
                    <marker id="blueArrow" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                        <polygon points="0 0, 10 3.5, 0 7" fill="#667eea"/>
                    </marker>
                </defs>
            </svg>
        </div>

        <h3>工具选择指南</h3>
        <div class="feature-grid">
            <div class="feature-item">
                <h4>文件探索</h4>
                <p><strong>LS</strong> - 浏览目录结构<br>
                <strong>Glob</strong> - 按名称查找文件<br>
                <strong>Grep</strong> - 按内容搜索文件</p>
            </div>
            <div class="feature-item">
                <h4>文件操作</h4>
                <p><strong>View</strong> - 查看文件内容<br>
                <strong>Edit</strong> - 精确修改<br>
                <strong>Write</strong> - 完整重写</p>
            </div>
            <div class="feature-item">
                <h4>批量操作</h4>
                <p><strong>Patch</strong> - 多文件修改<br>
                <strong>Bash</strong> - 命令行操作<br>
                <strong>Fetch</strong> - 网络资源</p>
            </div>
            <div class="feature-item">
                <h4>质量保证</h4>
                <p><strong>Diagnostics</strong> - 语法检查<br>
                <strong>Bash</strong> - 测试运行<br>
                <strong>权限系统</strong> - 安全控制</p>
            </div>
        </div>

        <h3>常见工作流程</h3>
        
        <h4>1. 新功能开发</h4>
        <div class="code-block">
1. LS 工具浏览项目结构
2. Glob 工具找到相关文件
3. View 工具查看现有代码
4. Edit/Write 工具实现功能
5. Diagnostics 检查语法
6. Bash 工具运行测试
</div>

        <h4>2. Bug 修复</h4>
        <div class="code-block">
1. Grep 工具搜索错误信息
2. View 工具查看问题代码
3. Edit 工具进行精确修改
4. Diagnostics 验证修复
5. Bash 工具测试修复效果
</div>

        <h4>3. 代码重构</h4>
        <div class="code-block">
1. Grep 工具找到所有相关代码
2. View 工具理解现有实现
3. Write 工具重写核心文件
4. Edit 工具调整相关文件
5. Patch 工具批量更新
6. 全面测试验证
</div>

        <div class="warning">
            <h4>注意事项</h4>
            <ul>
                <li>始终在编辑前使用 View 工具读取文件</li>
                <li>Edit 工具要求 old_string 在文件中唯一匹配</li>
                <li>Bash 工具有安全限制，某些命令被禁止</li>
                <li>所有文件操作都会记录历史和生成差异</li>
                <li>权限系统会拦截潜在危险操作</li>
            </ul>
        </div>

        <div class="tip">
            <h4>性能优化建议</h4>
            <ul>
                <li>使用 include 参数限制搜索范围</li>
                <li>优先使用 ripgrep 加速搜索</li>
                <li>避免在大目录上使用 LS 工具</li>
                <li>合理设置超时时间</li>
                <li>批量操作时使用 Patch 工具</li>
            </ul>
        </div>
    </div>

    <div class="section">
        <h2>总结</h2>
        <p>OpenCode Tools 提供了一套完整的 AI 代码助手工具集，涵盖了文件操作、搜索、执行和实用功能。通过合理使用这些工具，可以高效地进行代码开发、调试和维护工作。</p>
        
        <p>每个工具都经过精心设计，具有统一的接口、完善的权限控制和详细的错误处理。结合最佳实践，这些工具能够为 AI 代码助手提供强大而安全的操作能力。</p>
        
        <div class="tip">
            <h4>持续改进</h4>
            <p>工具集在不断演进中，会根据使用反馈和需求变化持续优化和扩展功能。建议定期查看更新和最佳实践指南。</p>
        </div>
    </div>
</body>
</html>